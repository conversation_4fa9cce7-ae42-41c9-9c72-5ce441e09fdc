import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  EyeSlashIcon,
  PencilIcon,
  TrashIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';

export default function ProjectEnvironment() {
  const project = useOutletContext<any>();
  const [searchTerm, setSearchTerm] = useState('');
  const [showValues, setShowValues] = useState<Record<string, boolean>>({});
  const [isAddingVariable, setIsAddingVariable] = useState(false);
  const [newVarKey, setNewVarKey] = useState('');
  const [newVarValue, setNewVarValue] = useState('');

  // If project data is not available, show a loading state
  if (!project) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-white/10 rounded w-1/4"></div>
          <div className="h-4 bg-white/10 rounded w-1/2 mt-2"></div>
          <div className="mt-6 h-10 bg-white/10 rounded"></div>
          <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
            <div className="p-8 flex justify-center">
              <div className="h-6 bg-white/10 rounded w-1/3"></div>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  // Filter environment variables based on search term
  const filteredEnvVars = Array.isArray(project?.environmentVars)
    ? project.environmentVars.filter((env: any) =>
        env.key.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : [];

  const toggleValueVisibility = (key: string) => {
    setShowValues(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleAddVariable = () => {
    if (newVarKey.trim() && newVarValue.trim()) {
      // In a real app, this would make an API call to add the variable
      // For now, we'll just simulate it

      // Reset form
      setNewVarKey('');
      setNewVarValue('');
      setIsAddingVariable(false);

      // Show success message
      alert('Environment variable added successfully!');
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-white">Environment Variables</h2>
            <p className="text-sm text-gray-400">Configure environment-specific config and secrets</p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              icon={<ArrowPathIcon className="h-4 w-4" />}
            >
              Refresh
            </Button>
            <Button
              variant="glass"
              size="sm"
              icon={<PlusIcon className="h-4 w-4" />}
              onClick={() => setIsAddingVariable(true)}
            >
              Add Variable
            </Button>
          </div>
        </div>

        {isAddingVariable && (
          <div className="mb-6 p-4 border border-white/10 rounded-xl bg-white/5">
            <h3 className="text-sm font-semibold text-white mb-4">Add Environment Variable</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="var-key" className="block text-sm font-medium text-gray-400 mb-1">
                  Key
                </label>
                <input
                  type="text"
                  id="var-key"
                  className="block w-full rounded-xl border-0 py-2 px-4 text-white ring-1 ring-inset ring-white/10
                    placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                    sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                  placeholder="DATABASE_URL"
                  value={newVarKey}
                  onChange={(e) => setNewVarKey(e.target.value)}
                />
              </div>
              <div>
                <label htmlFor="var-value" className="block text-sm font-medium text-gray-400 mb-1">
                  Value
                </label>
                <input
                  type="text"
                  id="var-value"
                  className="block w-full rounded-xl border-0 py-2 px-4 text-white ring-1 ring-inset ring-white/10
                    placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                    sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                  placeholder="postgres://user:password@localhost:5432/db"
                  value={newVarValue}
                  onChange={(e) => setNewVarValue(e.target.value)}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsAddingVariable(false)}
              >
                Cancel
              </Button>
              <Button
                variant="glass"
                size="sm"
                onClick={handleAddVariable}
              >
                Add Variable
              </Button>
            </div>
          </div>
        )}

        <div className="relative flex-grow max-w-md mb-6">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10
              placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
              sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
            placeholder="Search environment variables"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
          <table className="min-w-full divide-y divide-white/10">
            <thead>
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                  Key
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Value
                </th>
                <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {filteredEnvVars.length > 0 ? (
                filteredEnvVars.map((env: any) => (
                  <tr key={env.key} className="hover:bg-white/5 transition-colors duration-200">
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                      {env.key}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      <div className="flex items-center">
                        <span className="font-mono">
                          {showValues[env.key] ? env.value : '••••••••••••'}
                        </span>
                        <button
                          type="button"
                          className="ml-2 text-gray-400 hover:text-white transition-colors duration-200"
                          onClick={() => toggleValueVisibility(env.key)}
                        >
                          {showValues[env.key] ? (
                            <EyeSlashIcon className="h-4 w-4" />
                          ) : (
                            <EyeIcon className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </td>
                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="p-1"
                          title="Edit"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="p-1"
                          title="Delete"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} className="py-8 text-center text-gray-400">
                    No environment variables found matching your search.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-white">Secret Files</h2>
            <p className="text-sm text-gray-400">Store plaintext files containing secret data</p>
          </div>
          <Button
            variant="glass"
            size="sm"
            icon={<PlusIcon className="h-4 w-4" />}
          >
            Add Secret File
          </Button>
        </div>

        <div className="p-8 text-center border border-dashed border-white/20 rounded-xl">
          <p className="text-gray-400 mb-4">No secret files have been added yet.</p>
          <Button
            variant="outline"
            size="sm"
            icon={<PlusIcon className="h-4 w-4" />}
          >
            Add Secret File
          </Button>
        </div>
      </Card>
    </div>
  );
}
