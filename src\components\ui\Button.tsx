import { ButtonHTMLAttributes, ReactNode } from 'react';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'glass';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  glow?: boolean;
  icon?: ReactNode;
  as?: any; // For rendering as different elements like Link
}

export default function Button({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  className = '',
  glow = false,
  icon,
  as: Component = 'button',
  ...restProps
}: ButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200';

  const variantClasses = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',
    secondary: 'bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500',
    outline: 'border border-white/20 bg-transparent text-white hover:bg-white/5 focus:ring-secondary-500',
    ghost: 'text-white hover:bg-white/5 focus:ring-secondary-500',
    glass: 'bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 focus:ring-secondary-500',
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  const widthClass = fullWidth ? 'w-full' : '';
  const glowClass = glow ? 'shadow-lg shadow-secondary-500/20 hover:shadow-secondary-500/40' : '';

  const combinedClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass} ${glowClass} ${className}`;

  // Filter out custom props that shouldn't be passed to the DOM
  const { glow: _, icon: __, ...domProps } = restProps;

  return (
    <Component className={combinedClasses} {...domProps}>
      {icon && <span className="mr-2">{icon}</span>}
      {children}
    </Component>
  );
}
