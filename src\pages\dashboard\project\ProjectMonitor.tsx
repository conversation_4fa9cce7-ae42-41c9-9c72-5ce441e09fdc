import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import {
  ArrowPathIcon,
  ClockIcon,
  ServerIcon,
  CpuChipIcon,
  CircleStackIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

export default function ProjectMonitor() {
  const project = useOutletContext<any>();
  const [timeRange, setTimeRange] = useState('1h');
  const [autoRefresh, setAutoRefresh] = useState(false);

  // Sample data for charts
  const labels = ['1h ago', '50m ago', '40m ago', '30m ago', '20m ago', '10m ago', 'Now'];

  const cpuChartData = {
    labels,
    datasets: [
      {
        label: 'CPU Usage (%)',
        data: project?.metrics?.cpu || [45, 42, 47, 50, 48, 46, 44],
        borderColor: 'rgba(139, 92, 246, 1)',
        backgroundColor: 'rgba(139, 92, 246, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const memoryChartData = {
    labels,
    datasets: [
      {
        label: 'Memory Usage (MB)',
        data: project?.metrics?.memory || [512, 520, 510, 530, 525, 515, 518],
        borderColor: 'rgba(14, 165, 233, 1)',
        backgroundColor: 'rgba(14, 165, 233, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const networkChartData = {
    labels,
    datasets: [
      {
        label: 'Network In (KB/s)',
        data: [120, 145, 132, 158, 142, 138, 150],
        borderColor: 'rgba(34, 197, 94, 1)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        fill: true,
        tension: 0.4,
      },
      {
        label: 'Network Out (KB/s)',
        data: [85, 92, 88, 95, 90, 87, 93],
        borderColor: 'rgba(239, 68, 68, 1)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.7)',
          font: {
            family: 'Poppins, sans-serif',
          },
        },
      },
    },
    scales: {
      y: {
        ticks: {
          color: 'rgba(255, 255, 255, 0.7)',
          font: {
            family: 'Poppins, sans-serif',
          },
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      x: {
        ticks: {
          color: 'rgba(255, 255, 255, 0.7)',
          font: {
            family: 'Poppins, sans-serif',
          },
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-white">Resource Monitoring</h2>
          <p className="text-sm text-gray-400">Track your application's performance metrics in real-time</p>
        </div>
        <div className="flex space-x-2">
          <select
            className="rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10
              focus:ring-2 focus:ring-inset focus:ring-secondary-500
              sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <option value="1h">Last hour</option>
            <option value="6h">Last 6 hours</option>
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
          </select>

          <Button
            variant={autoRefresh ? "glass" : "outline"}
            size="sm"
            icon={<ArrowPathIcon className={`h-4 w-4 ${autoRefresh ? 'animate-spin' : ''}`} />}
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? 'Auto-refreshing' : 'Refresh'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-4 relative overflow-hidden">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">CPU Usage</h3>
            <div className="flex items-center">
              <CpuChipIcon className="h-4 w-4 text-secondary-400 mr-1" />
              <span className="text-lg font-semibold text-white">{project?.metrics?.cpu?.[6] || 44}%</span>
            </div>
          </div>
          <div className="h-20">
            <Line data={cpuChartData} options={{...chartOptions, plugins: {legend: {display: false}}}} />
          </div>
        </Card>

        <Card className="p-4 relative overflow-hidden">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Memory Usage</h3>
            <div className="flex items-center">
              <CircleStackIcon className="h-4 w-4 text-primary-400 mr-1" />
              <span className="text-lg font-semibold text-white">{project?.metrics?.memory?.[6] || 518} MB</span>
            </div>
          </div>
          <div className="h-20">
            <Line data={memoryChartData} options={{...chartOptions, plugins: {legend: {display: false}}}} />
          </div>
        </Card>

        <Card className="p-4 relative overflow-hidden">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Network Traffic</h3>
            <div className="flex items-center space-x-2">
              <div className="flex items-center">
                <ArrowDownIcon className="h-3 w-3 text-green-400 mr-1" />
                <span className="text-sm font-medium text-white">150 KB/s</span>
              </div>
              <div className="flex items-center">
                <ArrowUpIcon className="h-3 w-3 text-red-400 mr-1" />
                <span className="text-sm font-medium text-white">93 KB/s</span>
              </div>
            </div>
          </div>
          <div className="h-20">
            <Line data={networkChartData} options={{...chartOptions, plugins: {legend: {display: false}}}} />
          </div>
        </Card>
      </div>

      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-base font-semibold leading-6 text-white">Detailed Metrics</h3>
          <div className="flex items-center text-sm text-gray-400">
            <ClockIcon className="h-4 w-4 mr-1" />
            <span>Last updated: 2 minutes ago</span>
          </div>
        </div>
        <div className="h-80">
          <Line data={cpuChartData} options={chartOptions} />
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-base font-semibold leading-6 text-white">Memory Usage</h3>
          <Button
            variant="outline"
            size="sm"
            icon={<ServerIcon className="h-4 w-4" />}
          >
            Scale Resources
          </Button>
        </div>
        <div className="h-80">
          <Line data={memoryChartData} options={chartOptions} />
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-base font-semibold leading-6 text-white">Network Traffic</h3>
          <Button
            variant="outline"
            size="sm"
          >
            View Details
          </Button>
        </div>
        <div className="h-80">
          <Line data={networkChartData} options={chartOptions} />
        </div>
      </Card>
    </div>
  );
}
