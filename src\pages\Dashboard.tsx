import { useState } from 'react';
import { Bars3Icon } from '@heroicons/react/24/outline';
import Sidebar from '../components/dashboard/Sidebar';
import HostingStats from '../components/dashboard/HostingStats';
import DomainList from '../components/dashboard/DomainList';

// Sample data
const stats = [
  { name: 'CPU Usage', value: '45%', change: '12%', changeType: 'decrease' },
  { name: 'Memory Usage', value: '1.2 GB', change: '8%', changeType: 'increase' },
  { name: 'Storage', value: '8.2 GB', change: '5%', changeType: 'increase' },
  { name: 'Bandwidth', value: '42 GB', change: '3%', changeType: 'decrease' },
];

const domains = [
  {
    id: '1',
    name: 'example.com',
    status: 'active',
    expiresAt: 'May 12, 2025',
    autoRenew: true,
  },
  {
    id: '2',
    name: 'myapp.io',
    status: 'active',
    expiresAt: 'Jan 3, 2026',
    autoRenew: true,
  },
  {
    id: '3',
    name: 'test-site.dev',
    status: 'pending',
    expiresAt: 'Oct 22, 2025',
    autoRenew: false,
  },
  {
    id: '4',
    name: 'old-project.net',
    status: 'expired',
    expiresAt: 'Mar 15, 2024',
    autoRenew: false,
  },
];

export default function Dashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div>
      <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      <div className="lg:pl-72">
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 dark:text-gray-300 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>

          {/* Separator */}
          <div className="h-6 w-px bg-gray-200 dark:bg-gray-700 lg:hidden" aria-hidden="true" />

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex-1" />
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              <div className="hidden sm:flex sm:items-center sm:ml-6">
                <div className="relative ml-3">
                  <div className="flex items-center">
                    <div className="h-8 w-8 rounded-full bg-secondary-600 flex items-center justify-center text-white">
                      JD
                    </div>
                    <span className="ml-2 text-sm font-medium text-gray-900 dark:text-white">John Doe</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <main className="py-10">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="sm:flex sm:items-center">
              <div className="sm:flex-auto">
                <h1 className="text-xl font-semibold leading-6 text-gray-900 dark:text-white">Dashboard</h1>
                <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
                  Overview of your hosting resources, domains, and recent activity.
                </p>
              </div>
            </div>

            <div className="mt-8">
              <HostingStats stats={stats} />
            </div>

            <div className="mt-8">
              <h2 className="text-base font-semibold leading-6 text-gray-900 dark:text-white mb-4">Your Domains</h2>
              <DomainList domains={domains} />
            </div>

            <div className="mt-8">
              <h2 className="text-base font-semibold leading-6 text-gray-900 dark:text-white mb-4">Recent Activity</h2>
              <div className="overflow-hidden bg-white dark:bg-gray-800 shadow sm:rounded-md">
                <ul role="list" className="divide-y divide-gray-200 dark:divide-gray-700">
                  <li>
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <p className="truncate text-sm font-medium text-secondary-600">Deployment successful</p>
                        <div className="ml-2 flex flex-shrink-0">
                          <p className="inline-flex rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800">
                            Success
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 sm:flex sm:justify-between">
                        <div className="sm:flex">
                          <p className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            example.com
                          </p>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0">
                          <p>
                            <time dateTime="2023-01-23T13:23Z">3 hours ago</time>
                          </p>
                        </div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <p className="truncate text-sm font-medium text-secondary-600">Domain renewed</p>
                        <div className="ml-2 flex flex-shrink-0">
                          <p className="inline-flex rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800">
                            Completed
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 sm:flex sm:justify-between">
                        <div className="sm:flex">
                          <p className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            myapp.io
                          </p>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0">
                          <p>
                            <time dateTime="2023-01-23T09:12Z">Yesterday</time>
                          </p>
                        </div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <p className="truncate text-sm font-medium text-secondary-600">SSL certificate issued</p>
                        <div className="ml-2 flex flex-shrink-0">
                          <p className="inline-flex rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800">
                            Completed
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 sm:flex sm:justify-between">
                        <div className="sm:flex">
                          <p className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            test-site.dev
                          </p>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0">
                          <p>
                            <time dateTime="2023-01-22T11:43Z">2 days ago</time>
                          </p>
                        </div>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
