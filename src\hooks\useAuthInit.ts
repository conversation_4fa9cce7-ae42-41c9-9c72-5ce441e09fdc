import { useEffect } from 'react';
import useAuthStore from '../stores/authStore';

/**
 * Hook to initialize authentication state on app load
 * Fetches user profile if there's a valid token in localStorage
 */
export function useAuthInit() {
  const { isAuthenticated, isTokenExpired, fetchUserProfile, user } = useAuthStore();

  useEffect(() => {
    const initializeAuth = async () => {
      // Only fetch profile if authenticated, token is valid, and we don't have user data
      if (isAuthenticated && !isTokenExpired() && !user) {
        console.log('🔄 Initializing auth: fetching user profile...');
        await fetchUserProfile();
      }
    };

    initializeAuth();
  }, [isAuthenticated, isTokenExpired, fetchUserProfile, user]);
}

export default useAuthInit;
