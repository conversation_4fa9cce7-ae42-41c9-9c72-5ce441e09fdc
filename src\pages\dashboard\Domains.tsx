import { useState } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  PlusIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowPathIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';
import DomainList from '../../components/dashboard/DomainList';

// Sample data for domains
const domains = [
  {
    id: '1',
    name: 'example.com',
    status: 'active',
    expiresAt: 'May 12, 2024',
    autoRenew: true,
    ssl: true,
    dns: [
      { type: 'A', name: '@', value: '192.168.1.1', ttl: 3600 },
      { type: 'CNAME', name: 'www', value: 'example.com', ttl: 3600 },
      { type: 'MX', name: '@', value: 'mail.example.com', ttl: 3600, priority: 10 },
    ],
  },
  {
    id: '2',
    name: 'myapp.io',
    status: 'active',
    expiresAt: 'Jan 3, 2025',
    autoRenew: true,
    ssl: true,
    dns: [
      { type: 'A', name: '@', value: '192.168.1.2', ttl: 3600 },
      { type: 'CNAME', name: 'www', value: 'myapp.io', ttl: 3600 },
    ],
  },
  {
    id: '3',
    name: 'test-site.dev',
    status: 'active',
    expiresAt: 'Nov 15, 2024',
    autoRenew: false,
    ssl: true,
    dns: [
      { type: 'A', name: '@', value: '192.168.1.3', ttl: 3600 },
    ],
  },
  {
    id: '4',
    name: 'new-project.com',
    status: 'pending',
    expiresAt: 'Oct 22, 2024',
    autoRenew: true,
    ssl: false,
    dns: [],
  },
  {
    id: '5',
    name: 'old-site.net',
    status: 'expired',
    expiresAt: 'Mar 5, 2023',
    autoRenew: false,
    ssl: false,
    dns: [],
  },
];

// Domain availability check results
const availabilityResults = [
  { domain: 'awesome-domain.com', available: true, price: '$12.99/year' },
  { domain: 'awesome-domain.io', available: true, price: '$39.99/year' },
  { domain: 'awesome-domain.dev', available: true, price: '$15.99/year' },
  { domain: 'awesome-domain.net', available: true, price: '$11.99/year' },
  { domain: 'awesome-domain.org', available: false, price: 'N/A' },
];

export default function Domains() {
  const [searchTerm, setSearchTerm] = useState('');
  const [domainToCheck, setDomainToCheck] = useState('');
  const [showAvailability, setShowAvailability] = useState(false);
  const [activeTab, setActiveTab] = useState('domains');
  
  const filteredDomains = domains.filter(domain => 
    domain.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleDomainCheck = (e: React.FormEvent) => {
    e.preventDefault();
    if (domainToCheck) {
      setShowAvailability(true);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Domain Management</h1>
          <p className="mt-1 text-sm text-gray-400">Register, connect, and manage your domains</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant={activeTab === 'domains' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('domains')}
          >
            My Domains
          </Button>
          <Button 
            variant={activeTab === 'register' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('register')}
          >
            Register New
          </Button>
        </div>
      </div>
      
      {activeTab === 'domains' ? (
        <DomainList domains={filteredDomains} />
      ) : (
        <Card className="p-6">
          <div className="max-w-3xl mx-auto">
            <div className="text-center mb-8">
              <GlobeAltIcon className="h-12 w-12 text-secondary-500 mx-auto mb-4" />
              <h2 className="text-xl font-bold text-white mb-2">Find Your Perfect Domain</h2>
              <p className="text-gray-400">Search for available domains and register them instantly</p>
            </div>
            
            <form onSubmit={handleDomainCheck} className="mb-8">
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative flex-grow">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    className="block w-full rounded-xl border-0 py-3 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10 
                      placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                      sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                    placeholder="Enter domain name (e.g., awesome-domain)"
                    value={domainToCheck}
                    onChange={(e) => setDomainToCheck(e.target.value)}
                  />
                </div>
                <Button 
                  type="submit"
                  variant="glass" 
                  glow={true}
                >
                  Check Availability
                </Button>
              </div>
            </form>
            
            {showAvailability && (
              <div className="space-y-4 animate-fade-in">
                <h3 className="text-lg font-medium text-white mb-4">Availability Results</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {availabilityResults.map((result, index) => (
                    <div 
                      key={result.domain} 
                      className="glass-card p-4 rounded-xl flex justify-between items-center"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="flex items-center">
                        {result.available ? (
                          <CheckCircleIcon className="h-5 w-5 text-green-400 mr-3" />
                        ) : (
                          <XCircleIcon className="h-5 w-5 text-red-400 mr-3" />
                        )}
                        <div>
                          <p className="text-sm font-medium text-white">{result.domain}</p>
                          <p className="text-xs text-gray-400">{result.price}</p>
                        </div>
                      </div>
                      {result.available && (
                        <Button 
                          variant="glass" 
                          size="sm"
                        >
                          Register
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
                
                <div className="mt-6 text-center">
                  <p className="text-sm text-gray-400 mb-4">
                    Can't find what you're looking for? Try different extensions or variations.
                  </p>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      setDomainToCheck('');
                      setShowAvailability(false);
                    }}
                  >
                    New Search
                  </Button>
                </div>
              </div>
            )}
            
            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="glass-card p-5 rounded-xl text-center">
                <div className="h-12 w-12 rounded-full bg-secondary-900/50 border border-secondary-500/30 flex items-center justify-center mx-auto mb-4">
                  <GlobeAltIcon className="h-6 w-6 text-secondary-400" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Domain Registration</h3>
                <p className="text-sm text-gray-400 mb-4">Register new domains with competitive pricing</p>
              </div>
              
              <div className="glass-card p-5 rounded-xl text-center">
                <div className="h-12 w-12 rounded-full bg-secondary-900/50 border border-secondary-500/30 flex items-center justify-center mx-auto mb-4">
                  <ShieldCheckIcon className="h-6 w-6 text-secondary-400" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Free SSL Certificates</h3>
                <p className="text-sm text-gray-400 mb-4">Automatic SSL certificates for all your domains</p>
              </div>
              
              <div className="glass-card p-5 rounded-xl text-center">
                <div className="h-12 w-12 rounded-full bg-secondary-900/50 border border-secondary-500/30 flex items-center justify-center mx-auto mb-4">
                  <ArrowPathIcon className="h-6 w-6 text-secondary-400" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Auto Renewal</h3>
                <p className="text-sm text-gray-400 mb-4">Never lose your domain with automatic renewals</p>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}
