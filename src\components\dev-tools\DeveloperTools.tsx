import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  FolderIcon,
  CodeBracketIcon,
  CommandLineIcon,
  ServerIcon,
  CubeIcon,
  DocumentTextIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  Cog6ToothIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  PencilIcon,
  EyeIcon,
  Bars3Icon
} from '@heroicons/react/24/outline';
import FileManager from './FileManager';
import CodeEditor from './CodeEditor';
import Terminal from './Terminal';
import SSHManager from './SSHManager';

interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  content?: string;
  language?: string;
}

interface SSHConnection {
  id: string;
  name: string;
  host: string;
  port: number;
  username: string;
  password?: string;
  privateKey?: string;
  isConnected: boolean;
}

type SidebarTab = 'explorer' | 'search' | 'git' | 'debug' | 'extensions';
type BottomPanelTab = 'terminal' | 'problems' | 'output' | 'debug-console';

const DeveloperTools: React.FC = () => {
  const [activeSidebarTab, setActiveSidebarTab] = useState<SidebarTab>('explorer');
  const [activeBottomTab, setActiveBottomTab] = useState<BottomPanelTab>('terminal');
  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);
  const [fileContents, setFileContents] = useState<{ [key: string]: string }>({});
  const [openFiles, setOpenFiles] = useState<FileNode[]>([]);
  const [activeFileId, setActiveFileId] = useState<string | null>(null);
  const [isBottomPanelOpen, setIsBottomPanelOpen] = useState(true);
  const [bottomPanelHeight, setBottomPanelHeight] = useState(300);
  const [isMaximized, setIsMaximized] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartY, setDragStartY] = useState(0);
  const [dragStartHeight, setDragStartHeight] = useState(0);

  const containerRef = useRef<HTMLDivElement>(null);
  const resizeHandleRef = useRef<HTMLDivElement>(null);

  const handleFileSelect = useCallback((file: FileNode) => {
    // Load file content if not already loaded
    if (!fileContents[file.id]) {
      const mockContent = generateMockContent(file);
      setFileContents(prev => ({ ...prev, [file.id]: mockContent }));
      file.content = mockContent;
    } else {
      file.content = fileContents[file.id];
    }

    // Add to open files if not already open
    if (!openFiles.find(f => f.id === file.id)) {
      setOpenFiles(prev => [...prev, file]);
    }

    setSelectedFile(file);
    setActiveFileId(file.id);
  }, [fileContents, openFiles]);

  const handleFileSave = useCallback((file: FileNode, content: string) => {
    setFileContents(prev => ({ ...prev, [file.id]: content }));
    console.log(`Saving file: ${file.name}`, content);
    // Here you would implement actual file saving logic
  }, []);

  const handleFileRun = useCallback(async (file: FileNode) => {
    console.log(`Running file: ${file.name}`);
    // Here you would implement file execution logic
    setActiveTab('terminal');
  }, []);

  const handleSSHConnect = useCallback((connection: SSHConnection) => {
    console.log(`Connecting to SSH: ${connection.name}`);
    // Here you would implement SSH connection logic
    setActiveBottomTab('terminal');
    setIsBottomPanelOpen(true);
  }, []);

  const handleCloseFile = useCallback((fileId: string) => {
    setOpenFiles(prev => {
      const updated = prev.filter(f => f.id !== fileId);

      // If closing the active file, switch to another open file
      if (activeFileId === fileId) {
        if (updated.length > 0) {
          const newActiveFile = updated[updated.length - 1];
          setActiveFileId(newActiveFile.id);
          setSelectedFile(newActiveFile);
        } else {
          setActiveFileId(null);
          setSelectedFile(null);
        }
      }

      return updated;
    });
  }, [activeFileId]);

  const handleSwitchFile = useCallback((file: FileNode) => {
    setActiveFileId(file.id);
    setSelectedFile(file);
  }, []);

  // Drag functionality for bottom panel
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStartY(e.clientY);
    setDragStartHeight(bottomPanelHeight);
    e.preventDefault();
  }, [bottomPanelHeight]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    const deltaY = dragStartY - e.clientY;
    const newHeight = Math.max(100, Math.min(600, dragStartHeight + deltaY));
    setBottomPanelHeight(newHeight);
  }, [isDragging, dragStartY, dragStartHeight]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const getLanguageFromFilename = useCallback((filename: string): string => {
    const extension = filename.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'JavaScript',
      'jsx': 'JavaScript React',
      'ts': 'TypeScript',
      'tsx': 'TypeScript React',
      'html': 'HTML',
      'css': 'CSS',
      'scss': 'SCSS',
      'json': 'JSON',
      'md': 'Markdown',
      'py': 'Python',
      'php': 'PHP',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C',
      'go': 'Go',
      'rs': 'Rust',
      'rb': 'Ruby',
      'yml': 'YAML',
      'yaml': 'YAML',
      'xml': 'XML',
      'sql': 'SQL'
    };
    return languageMap[extension || ''] || 'Plain Text';
  }, []);

  const generateMockContent = (file: FileNode): string => {
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'js':
      case 'jsx':
        return `// ${file.name}
import React from 'react';

const Component = () => {
  return (
    <div>
      <h1>Hello World</h1>
    </div>
  );
};

export default Component;`;
      
      case 'html':
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <h1>Hello World</h1>
</body>
</html>`;
      
      case 'css':
        return `/* ${file.name} */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}`;
      
      case 'json':
        return `{
  "name": "project",
  "version": "1.0.0",
  "description": "A sample project",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "build": "webpack --mode production"
  },
  "dependencies": {
    "express": "^4.18.0",
    "react": "^18.0.0"
  }
}`;
      
      case 'md':
        return `# ${file.name.replace('.md', '')}

## Overview

This is a sample markdown file.

## Features

- Feature 1
- Feature 2
- Feature 3

## Installation

\`\`\`bash
npm install
npm start
\`\`\`

## Usage

\`\`\`javascript
const app = require('./app');
app.listen(3000);
\`\`\``;
      
      default:
        return `// ${file.name}
// This is a sample file
console.log('Hello from ${file.name}');`;
    }
  };

  const sidebarTabs = [
    { id: 'explorer' as SidebarTab, icon: FolderIcon, title: 'Explorer' },
    { id: 'search' as SidebarTab, icon: MagnifyingGlassIcon, title: 'Search' },
    { id: 'git' as SidebarTab, icon: CodeBracketIcon, title: 'Source Control' },
    { id: 'debug' as SidebarTab, icon: WrenchScrewdriverIcon, title: 'Run and Debug' },
    { id: 'extensions' as SidebarTab, icon: CubeIcon, title: 'Extensions' }
  ];

  const bottomPanelTabs = [
    { id: 'terminal' as BottomPanelTab, name: 'Terminal', icon: CommandLineIcon },
    { id: 'problems' as BottomPanelTab, name: 'Problems', icon: DocumentTextIcon },
    { id: 'output' as BottomPanelTab, name: 'Output', icon: ChartBarIcon },
    { id: 'debug-console' as BottomPanelTab, name: 'Debug Console', icon: WrenchScrewdriverIcon }
  ];

  // Panel Components
  const SearchPanel = () => (
    <div className="p-4">
      <div className="mb-4">
        <input
          type="text"
          placeholder="Search files..."
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:outline-none focus:border-blue-500"
        />
      </div>
      <div className="space-y-2">
        <div className="flex items-center space-x-2 text-sm">
          <input type="checkbox" className="rounded" />
          <span className="text-gray-300">Match Case</span>
        </div>
        <div className="flex items-center space-x-2 text-sm">
          <input type="checkbox" className="rounded" />
          <span className="text-gray-300">Match Whole Word</span>
        </div>
        <div className="flex items-center space-x-2 text-sm">
          <input type="checkbox" className="rounded" />
          <span className="text-gray-300">Use Regular Expression</span>
        </div>
      </div>
      <div className="mt-4 text-xs text-gray-500">
        <p>🔍 Global search across all files</p>
        <p>📁 Search in specific folders</p>
        <p>🔄 Replace functionality</p>
      </div>
    </div>
  );

  const GitPanel = () => (
    <div className="p-4">
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-300 mb-2">Source Control</h4>
        <div className="space-y-2">
          <button className="w-full px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">
            Initialize Repository
          </button>
          <button className="w-full px-3 py-2 bg-gray-700 text-gray-300 rounded text-sm hover:bg-gray-600">
            Clone Repository
          </button>
        </div>
      </div>
      <div className="text-xs text-gray-500">
        <p>🌿 Git version control</p>
        <p>📝 Commit changes</p>
        <p>🔄 Push/Pull from remote</p>
        <p>🌳 Branch management</p>
      </div>
    </div>
  );

  const DebugPanel = () => (
    <div className="p-4">
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-300 mb-2">Run and Debug</h4>
        <div className="space-y-2">
          <button className="w-full px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700">
            ▶ Start Debugging
          </button>
          <button className="w-full px-3 py-2 bg-gray-700 text-gray-300 rounded text-sm hover:bg-gray-600">
            ⚙ Configure Launch.json
          </button>
        </div>
      </div>
      <div className="text-xs text-gray-500">
        <p>🐛 Debug applications</p>
        <p>🔍 Set breakpoints</p>
        <p>📊 Variable inspection</p>
        <p>📈 Call stack analysis</p>
      </div>
    </div>
  );

  const ExtensionsPanel = () => (
    <div className="p-4">
      <div className="mb-4">
        <input
          type="text"
          placeholder="Search extensions..."
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:outline-none focus:border-blue-500"
        />
      </div>
      <div className="space-y-3">
        <div className="p-3 bg-gray-700 rounded">
          <h5 className="text-sm font-medium text-white">Prettier</h5>
          <p className="text-xs text-gray-400">Code formatter</p>
          <button className="mt-2 px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700">
            Install
          </button>
        </div>
        <div className="p-3 bg-gray-700 rounded">
          <h5 className="text-sm font-medium text-white">ESLint</h5>
          <p className="text-xs text-gray-400">JavaScript linter</p>
          <button className="mt-2 px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700">
            Install
          </button>
        </div>
      </div>
      <div className="mt-4 text-xs text-gray-500">
        <p>🧩 Extend functionality</p>
        <p>🎨 Themes and icons</p>
        <p>⚡ Language support</p>
      </div>
    </div>
  );

  const ProblemsPanel = () => (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-300">Problems</span>
          <span className="text-xs text-gray-500">0 errors, 0 warnings</span>
        </div>
      </div>
      <div className="flex-1 p-4 text-center text-gray-500">
        <DocumentTextIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No problems detected</p>
        <p className="text-xs mt-2">Errors and warnings will appear here</p>
      </div>
    </div>
  );

  const OutputPanel = () => (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-300">Output</span>
          <select className="text-xs bg-gray-700 border border-gray-600 rounded px-2 py-1 text-gray-300">
            <option>Tasks</option>
            <option>Git</option>
            <option>Extension Host</option>
          </select>
        </div>
      </div>
      <div className="flex-1 p-4 font-mono text-xs text-gray-300 bg-gray-900">
        <div className="space-y-1">
          <div className="text-blue-400">[INFO] PoolotHost IDE initialized</div>
          <div className="text-green-400">[SUCCESS] File system ready</div>
          <div className="text-yellow-400">[WARN] No workspace configuration found</div>
          <div className="text-gray-500">[DEBUG] Extension host started</div>
        </div>
      </div>
    </div>
  );

  const DebugConsolePanel = () => (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b border-gray-700">
        <span className="text-sm text-gray-300">Debug Console</span>
      </div>
      <div className="flex-1 p-4 font-mono text-xs text-gray-300 bg-gray-900">
        <div className="space-y-1">
          <div className="text-blue-400">Debug session not started</div>
          <div className="text-gray-500">Start debugging to see console output</div>
        </div>
      </div>
      <div className="p-2 border-t border-gray-700">
        <input
          type="text"
          placeholder="Evaluate expression..."
          className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:outline-none focus:border-blue-500"
        />
      </div>
    </div>
  );

  return (
    <div
      ref={containerRef}
      className={`h-full flex bg-gray-900 text-white ${isMaximized ? 'fixed inset-0 z-50' : ''}`}
    >
      {/* Activity Bar (Left) */}
      {!isMaximized && (
        <div className="w-12 bg-gray-800 flex flex-col border-r border-gray-700">
          {sidebarTabs.map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveSidebarTab(tab.id)}
                className={`p-3 hover:bg-gray-700 transition-colors ${
                  activeSidebarTab === tab.id ? 'bg-gray-700 border-r-2 border-blue-500' : ''
                }`}
                title={tab.title}
              >
                <Icon className="h-5 w-5 text-gray-300" />
              </button>
            );
          })}
          <div className="flex-1" />
          <button className="p-3 hover:bg-gray-700 transition-colors" title="Settings">
            <Cog6ToothIcon className="h-5 w-5 text-gray-300" />
          </button>
        </div>
      )}

      {/* Sidebar */}
      {!isMaximized && (
        <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
          {/* Sidebar Header */}
          <div className="p-3 border-b border-gray-700">
            <h2 className="text-sm font-medium text-gray-300 uppercase tracking-wide">
              {sidebarTabs.find(tab => tab.id === activeSidebarTab)?.title}
            </h2>
          </div>

          {/* Sidebar Content */}
          <div className="flex-1 overflow-hidden">
            {activeSidebarTab === 'explorer' && (
              <FileManager onFileSelect={handleFileSelect} selectedFile={selectedFile} />
            )}
            {activeSidebarTab === 'search' && (
              <SearchPanel />
            )}
            {activeSidebarTab === 'git' && (
              <GitPanel />
            )}
            {activeSidebarTab === 'debug' && (
              <DebugPanel />
            )}
            {activeSidebarTab === 'extensions' && (
              <ExtensionsPanel />
            )}
          </div>
        </div>
      )}

      {/* Main Editor Area */}
      <div className="flex-1 flex flex-col">
        {/* Editor Header with Controls */}
        <div className="flex items-center justify-between bg-gray-800 border-b border-gray-700 px-3 py-2">
          <div className="flex items-center space-x-2">
            {/* Editor Tabs */}
            {openFiles.length > 0 && (
              <div className="flex overflow-x-auto">
                {openFiles.map(file => (
                  <div
                    key={file.id}
                    className={`flex items-center px-3 py-1 border-r border-gray-700 cursor-pointer min-w-0 ${
                      activeFileId === file.id ? 'bg-gray-900' : 'hover:bg-gray-700'
                    }`}
                    onClick={() => handleSwitchFile(file)}
                  >
                    <span className="text-sm text-gray-300 truncate mr-2">{file.name}</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCloseFile(file.id);
                      }}
                      className="text-gray-500 hover:text-gray-300"
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Editor Controls */}
          <div className="flex items-center space-x-2">
            {selectedFile && (
              <>
                <button
                  onClick={() => setIsEditMode(!isEditMode)}
                  className={`p-1 rounded transition-colors ${
                    isEditMode
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                  }`}
                  title={isEditMode ? 'Switch to Read Mode' : 'Switch to Edit Mode'}
                >
                  {isEditMode ? <PencilIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                </button>
                <button
                  onClick={() => setIsMaximized(!isMaximized)}
                  className="p-1 text-gray-400 hover:text-gray-200 hover:bg-gray-700 rounded transition-colors"
                  title={isMaximized ? 'Exit Fullscreen' : 'Maximize Editor'}
                >
                  {isMaximized ? (
                    <ArrowsPointingInIcon className="h-4 w-4" />
                  ) : (
                    <ArrowsPointingOutIcon className="h-4 w-4" />
                  )}
                </button>
              </>
            )}
          </div>
        </div>

        {/* Editor Content */}
        <div className={`flex-1 ${isBottomPanelOpen && !isMaximized ? '' : 'h-full'}`}>
          {selectedFile ? (
            <CodeEditor
              file={selectedFile}
              onSave={handleFileSave}
              onRun={handleFileRun}
              isReadOnly={!isEditMode}
              isMaximized={isMaximized}
            />
          ) : (
            <div className="h-full flex items-center justify-center bg-gray-900 text-gray-400">
              <div className="text-center">
                <CodeBracketIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">Welcome to PoolotHost IDE</h3>
                <p className="text-sm">Open a file from the explorer to start editing</p>
                <div className="mt-4 text-xs text-gray-500">
                  <p>💡 Click files in the explorer to view them</p>
                  <p>🔧 Use the edit button to modify files</p>
                  <p>⛶ Maximize for distraction-free coding</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Bottom Panel */}
        {isBottomPanelOpen && !isMaximized && (
          <div className="bg-gray-800 border-t border-gray-700 relative">
            {/* Draggable Resize Handle */}
            <div
              ref={resizeHandleRef}
              className="absolute top-0 left-0 right-0 h-1 cursor-ns-resize hover:bg-blue-500 transition-colors z-10"
              onMouseDown={handleMouseDown}
              title="Drag to resize panel"
            />

            <div style={{ height: `${bottomPanelHeight}px` }}>
              {/* Bottom Panel Tabs */}
              <div className="flex items-center justify-between bg-gray-800 border-b border-gray-700">
                <div className="flex">
                  {bottomPanelTabs.map(tab => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveBottomTab(tab.id)}
                        className={`flex items-center space-x-2 px-3 py-2 text-sm ${
                          activeBottomTab === tab.id
                            ? 'text-white border-b-2 border-blue-500'
                            : 'text-gray-400 hover:text-gray-300'
                        }`}
                      >
                        <Icon className="h-4 w-4" />
                        <span>{tab.name}</span>
                      </button>
                    );
                  })}
                </div>
                <div className="flex items-center space-x-2 px-3">
                  <span className="text-xs text-gray-500">
                    <Bars3Icon className="h-3 w-3 inline mr-1" />
                    Drag to resize
                  </span>
                  <button
                    onClick={() => setIsBottomPanelOpen(false)}
                    className="text-gray-400 hover:text-gray-300"
                  >
                    <ChevronDownIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Bottom Panel Content */}
              <div className="h-full overflow-hidden" style={{ height: `${bottomPanelHeight - 40}px` }}>
                {activeBottomTab === 'terminal' && <Terminal />}
                {activeBottomTab === 'problems' && <ProblemsPanel />}
                {activeBottomTab === 'output' && <OutputPanel />}
                {activeBottomTab === 'debug-console' && <DebugConsolePanel />}
              </div>
            </div>
          </div>
        )}

        {/* Show Bottom Panel Button (when collapsed) */}
        {!isBottomPanelOpen && (
          <div className="bg-gray-800 border-t border-gray-700 p-2">
            <button
              onClick={() => setIsBottomPanelOpen(true)}
              className="flex items-center space-x-2 text-gray-400 hover:text-gray-300 text-sm"
            >
              <ChevronUpIcon className="h-4 w-4" />
              <span>Show Panel</span>
            </button>
          </div>
        )}
      </div>


    </div>
  );
};

export default DeveloperTools;
