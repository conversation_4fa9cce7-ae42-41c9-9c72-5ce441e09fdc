import React, { useState, useCallback } from 'react';
import {
  FolderIcon,
  CodeBracketIcon,
  CommandLineIcon,
  ServerIcon,
  CubeIcon,
  DocumentTextIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import FileManager from './FileManager';
import CodeEditor from './CodeEditor';
import Terminal from './Terminal';
import SSHManager from './SSHManager';

interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  content?: string;
  language?: string;
}

interface SSHConnection {
  id: string;
  name: string;
  host: string;
  port: number;
  username: string;
  password?: string;
  privateKey?: string;
  isConnected: boolean;
}

type TabType = 'files' | 'editor' | 'terminal' | 'ssh' | 'database' | 'logs' | 'tools' | 'monitoring';

interface Tab {
  id: TabType;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  component: React.ReactNode;
}

const DeveloperTools: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('files');
  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);
  const [fileContents, setFileContents] = useState<{ [key: string]: string }>({});

  const handleFileSelect = useCallback((file: FileNode) => {
    setSelectedFile(file);
    setActiveTab('editor');
    
    // Load file content if not already loaded
    if (!fileContents[file.id]) {
      // Simulate loading file content
      const mockContent = generateMockContent(file);
      setFileContents(prev => ({ ...prev, [file.id]: mockContent }));
      setSelectedFile({ ...file, content: mockContent });
    } else {
      setSelectedFile({ ...file, content: fileContents[file.id] });
    }
  }, [fileContents]);

  const handleFileSave = useCallback((file: FileNode, content: string) => {
    setFileContents(prev => ({ ...prev, [file.id]: content }));
    console.log(`Saving file: ${file.name}`, content);
    // Here you would implement actual file saving logic
  }, []);

  const handleFileRun = useCallback(async (file: FileNode) => {
    console.log(`Running file: ${file.name}`);
    // Here you would implement file execution logic
    setActiveTab('terminal');
  }, []);

  const handleSSHConnect = useCallback((connection: SSHConnection) => {
    console.log(`Connecting to SSH: ${connection.name}`);
    // Here you would implement SSH connection logic
    setActiveTab('terminal');
  }, []);

  const generateMockContent = (file: FileNode): string => {
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'js':
      case 'jsx':
        return `// ${file.name}
import React from 'react';

const Component = () => {
  return (
    <div>
      <h1>Hello World</h1>
    </div>
  );
};

export default Component;`;
      
      case 'html':
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <h1>Hello World</h1>
</body>
</html>`;
      
      case 'css':
        return `/* ${file.name} */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}`;
      
      case 'json':
        return `{
  "name": "project",
  "version": "1.0.0",
  "description": "A sample project",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "build": "webpack --mode production"
  },
  "dependencies": {
    "express": "^4.18.0",
    "react": "^18.0.0"
  }
}`;
      
      case 'md':
        return `# ${file.name.replace('.md', '')}

## Overview

This is a sample markdown file.

## Features

- Feature 1
- Feature 2
- Feature 3

## Installation

\`\`\`bash
npm install
npm start
\`\`\`

## Usage

\`\`\`javascript
const app = require('./app');
app.listen(3000);
\`\`\``;
      
      default:
        return `// ${file.name}
// This is a sample file
console.log('Hello from ${file.name}');`;
    }
  };

  const tabs: Tab[] = [
    {
      id: 'files',
      name: 'Files',
      icon: FolderIcon,
      component: <FileManager onFileSelect={handleFileSelect} selectedFile={selectedFile} />
    },
    {
      id: 'editor',
      name: 'Editor',
      icon: CodeBracketIcon,
      component: <CodeEditor file={selectedFile} onSave={handleFileSave} onRun={handleFileRun} />
    },
    {
      id: 'terminal',
      name: 'Terminal',
      icon: CommandLineIcon,
      component: <Terminal />
    },
    {
      id: 'ssh',
      name: 'SSH',
      icon: ServerIcon,
      component: <SSHManager onConnect={handleSSHConnect} />
    },
    {
      id: 'database',
      name: 'Database',
      icon: CubeIcon,
      component: (
        <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
          <div className="text-center">
            <CubeIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Database Manager</h3>
            <p className="text-sm">Connect to MySQL, PostgreSQL, MongoDB and more</p>
            <p className="text-xs mt-2 text-gray-400">Coming Soon</p>
          </div>
        </div>
      )
    },
    {
      id: 'logs',
      name: 'Logs',
      icon: DocumentTextIcon,
      component: (
        <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
          <div className="text-center">
            <DocumentTextIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Log Viewer</h3>
            <p className="text-sm">Real-time application and server logs</p>
            <p className="text-xs mt-2 text-gray-400">Coming Soon</p>
          </div>
        </div>
      )
    },
    {
      id: 'tools',
      name: 'Tools',
      icon: WrenchScrewdriverIcon,
      component: (
        <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
          <div className="text-center">
            <WrenchScrewdriverIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Developer Tools</h3>
            <p className="text-sm">Git integration, package managers, and more</p>
            <p className="text-xs mt-2 text-gray-400">Coming Soon</p>
          </div>
        </div>
      )
    },
    {
      id: 'monitoring',
      name: 'Monitor',
      icon: ChartBarIcon,
      component: (
        <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
          <div className="text-center">
            <ChartBarIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Performance Monitor</h3>
            <p className="text-sm">CPU, Memory, Network and Disk usage</p>
            <p className="text-xs mt-2 text-gray-400">Coming Soon</p>
          </div>
        </div>
      )
    }
  ];

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-800">
      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
        {tabs.map(tab => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{tab.name}</span>
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeTabData?.component}
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-100 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 text-xs text-gray-600 dark:text-gray-300">
        <div className="flex items-center space-x-4">
          <span>Active: {activeTabData?.name}</span>
          {selectedFile && <span>File: {selectedFile.name}</span>}
        </div>
        <div className="flex items-center space-x-4">
          <span>PoolotHost Developer Environment</span>
          <span className="text-green-500">● Ready</span>
        </div>
      </div>
    </div>
  );
};

export default DeveloperTools;
