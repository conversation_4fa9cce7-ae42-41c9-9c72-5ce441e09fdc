import React, { useState, useCallback } from 'react';
import {
  FolderIcon,
  CodeBracketIcon,
  CommandLineIcon,
  ServerIcon,
  CubeIcon,
  DocumentTextIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  Cog6ToothIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';
import FileManager from './FileManager';
import CodeEditor from './CodeEditor';
import Terminal from './Terminal';
import SSHManager from './SSHManager';

interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  content?: string;
  language?: string;
}

interface SSHConnection {
  id: string;
  name: string;
  host: string;
  port: number;
  username: string;
  password?: string;
  privateKey?: string;
  isConnected: boolean;
}

type SidebarTab = 'explorer' | 'search' | 'git' | 'debug' | 'extensions';
type BottomPanelTab = 'terminal' | 'problems' | 'output' | 'debug-console';

const DeveloperTools: React.FC = () => {
  const [activeSidebarTab, setActiveSidebarTab] = useState<SidebarTab>('explorer');
  const [activeBottomTab, setActiveBottomTab] = useState<BottomPanelTab>('terminal');
  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);
  const [fileContents, setFileContents] = useState<{ [key: string]: string }>({});
  const [openFiles, setOpenFiles] = useState<FileNode[]>([]);
  const [activeFileId, setActiveFileId] = useState<string | null>(null);
  const [isBottomPanelOpen, setIsBottomPanelOpen] = useState(true);
  const [bottomPanelHeight, setBottomPanelHeight] = useState(300);

  const handleFileSelect = useCallback((file: FileNode) => {
    // Load file content if not already loaded
    if (!fileContents[file.id]) {
      const mockContent = generateMockContent(file);
      setFileContents(prev => ({ ...prev, [file.id]: mockContent }));
      file.content = mockContent;
    } else {
      file.content = fileContents[file.id];
    }

    // Add to open files if not already open
    if (!openFiles.find(f => f.id === file.id)) {
      setOpenFiles(prev => [...prev, file]);
    }

    setSelectedFile(file);
    setActiveFileId(file.id);
  }, [fileContents, openFiles]);

  const handleFileSave = useCallback((file: FileNode, content: string) => {
    setFileContents(prev => ({ ...prev, [file.id]: content }));
    console.log(`Saving file: ${file.name}`, content);
    // Here you would implement actual file saving logic
  }, []);

  const handleFileRun = useCallback(async (file: FileNode) => {
    console.log(`Running file: ${file.name}`);
    // Here you would implement file execution logic
    setActiveTab('terminal');
  }, []);

  const handleSSHConnect = useCallback((connection: SSHConnection) => {
    console.log(`Connecting to SSH: ${connection.name}`);
    // Here you would implement SSH connection logic
    setActiveBottomTab('terminal');
    setIsBottomPanelOpen(true);
  }, []);

  const handleCloseFile = useCallback((fileId: string) => {
    setOpenFiles(prev => {
      const updated = prev.filter(f => f.id !== fileId);

      // If closing the active file, switch to another open file
      if (activeFileId === fileId) {
        if (updated.length > 0) {
          const newActiveFile = updated[updated.length - 1];
          setActiveFileId(newActiveFile.id);
          setSelectedFile(newActiveFile);
        } else {
          setActiveFileId(null);
          setSelectedFile(null);
        }
      }

      return updated;
    });
  }, [activeFileId]);

  const handleSwitchFile = useCallback((file: FileNode) => {
    setActiveFileId(file.id);
    setSelectedFile(file);
  }, []);

  const getLanguageFromFilename = useCallback((filename: string): string => {
    const extension = filename.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'JavaScript',
      'jsx': 'JavaScript React',
      'ts': 'TypeScript',
      'tsx': 'TypeScript React',
      'html': 'HTML',
      'css': 'CSS',
      'scss': 'SCSS',
      'json': 'JSON',
      'md': 'Markdown',
      'py': 'Python',
      'php': 'PHP',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C',
      'go': 'Go',
      'rs': 'Rust',
      'rb': 'Ruby',
      'yml': 'YAML',
      'yaml': 'YAML',
      'xml': 'XML',
      'sql': 'SQL'
    };
    return languageMap[extension || ''] || 'Plain Text';
  }, []);

  const generateMockContent = (file: FileNode): string => {
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'js':
      case 'jsx':
        return `// ${file.name}
import React from 'react';

const Component = () => {
  return (
    <div>
      <h1>Hello World</h1>
    </div>
  );
};

export default Component;`;
      
      case 'html':
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <h1>Hello World</h1>
</body>
</html>`;
      
      case 'css':
        return `/* ${file.name} */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}`;
      
      case 'json':
        return `{
  "name": "project",
  "version": "1.0.0",
  "description": "A sample project",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "build": "webpack --mode production"
  },
  "dependencies": {
    "express": "^4.18.0",
    "react": "^18.0.0"
  }
}`;
      
      case 'md':
        return `# ${file.name.replace('.md', '')}

## Overview

This is a sample markdown file.

## Features

- Feature 1
- Feature 2
- Feature 3

## Installation

\`\`\`bash
npm install
npm start
\`\`\`

## Usage

\`\`\`javascript
const app = require('./app');
app.listen(3000);
\`\`\``;
      
      default:
        return `// ${file.name}
// This is a sample file
console.log('Hello from ${file.name}');`;
    }
  };

  const sidebarTabs = [
    { id: 'explorer' as SidebarTab, icon: FolderIcon, title: 'Explorer' },
    { id: 'search' as SidebarTab, icon: MagnifyingGlassIcon, title: 'Search' },
    { id: 'git' as SidebarTab, icon: CodeBracketIcon, title: 'Source Control' },
    { id: 'debug' as SidebarTab, icon: WrenchScrewdriverIcon, title: 'Run and Debug' },
    { id: 'extensions' as SidebarTab, icon: CubeIcon, title: 'Extensions' }
  ];

  const bottomPanelTabs = [
    { id: 'terminal' as BottomPanelTab, name: 'Terminal', icon: CommandLineIcon },
    { id: 'problems' as BottomPanelTab, name: 'Problems', icon: DocumentTextIcon },
    { id: 'output' as BottomPanelTab, name: 'Output', icon: ChartBarIcon },
    { id: 'debug-console' as BottomPanelTab, name: 'Debug Console', icon: WrenchScrewdriverIcon }
  ];

  return (
    <div className="h-full flex bg-gray-900 text-white">
      {/* Activity Bar (Left) */}
      <div className="w-12 bg-gray-800 flex flex-col border-r border-gray-700">
        {sidebarTabs.map(tab => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveSidebarTab(tab.id)}
              className={`p-3 hover:bg-gray-700 transition-colors ${
                activeSidebarTab === tab.id ? 'bg-gray-700 border-r-2 border-blue-500' : ''
              }`}
              title={tab.title}
            >
              <Icon className="h-5 w-5 text-gray-300" />
            </button>
          );
        })}
        <div className="flex-1" />
        <button className="p-3 hover:bg-gray-700 transition-colors" title="Settings">
          <Cog6ToothIcon className="h-5 w-5 text-gray-300" />
        </button>
      </div>

      {/* Sidebar */}
      <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
        {/* Sidebar Header */}
        <div className="p-3 border-b border-gray-700">
          <h2 className="text-sm font-medium text-gray-300 uppercase tracking-wide">
            {sidebarTabs.find(tab => tab.id === activeSidebarTab)?.title}
          </h2>
        </div>

        {/* Sidebar Content */}
        <div className="flex-1 overflow-hidden">
          {activeSidebarTab === 'explorer' && (
            <FileManager onFileSelect={handleFileSelect} selectedFile={selectedFile} />
          )}
          {activeSidebarTab === 'search' && (
            <div className="p-4 text-gray-400 text-center">
              <MagnifyingGlassIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Search functionality coming soon</p>
            </div>
          )}
          {activeSidebarTab === 'git' && (
            <div className="p-4 text-gray-400 text-center">
              <CodeBracketIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Git integration coming soon</p>
            </div>
          )}
          {activeSidebarTab === 'debug' && (
            <div className="p-4 text-gray-400 text-center">
              <WrenchScrewdriverIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Debug tools coming soon</p>
            </div>
          )}
          {activeSidebarTab === 'extensions' && (
            <div className="p-4 text-gray-400 text-center">
              <CubeIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Extensions coming soon</p>
            </div>
          )}
        </div>
      </div>

      {/* Main Editor Area */}
      <div className="flex-1 flex flex-col">
        {/* Editor Tabs */}
        {openFiles.length > 0 && (
          <div className="flex bg-gray-800 border-b border-gray-700 overflow-x-auto">
            {openFiles.map(file => (
              <div
                key={file.id}
                className={`flex items-center px-3 py-2 border-r border-gray-700 cursor-pointer min-w-0 ${
                  activeFileId === file.id ? 'bg-gray-900' : 'hover:bg-gray-700'
                }`}
                onClick={() => handleSwitchFile(file)}
              >
                <span className="text-sm text-gray-300 truncate mr-2">{file.name}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCloseFile(file.id);
                  }}
                  className="text-gray-500 hover:text-gray-300"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Editor Content */}
        <div className={`flex-1 ${isBottomPanelOpen ? '' : 'h-full'}`}>
          {selectedFile ? (
            <CodeEditor
              file={selectedFile}
              onSave={handleFileSave}
              onRun={handleFileRun}
            />
          ) : (
            <div className="h-full flex items-center justify-center bg-gray-900 text-gray-400">
              <div className="text-center">
                <CodeBracketIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">Welcome to PoolotHost IDE</h3>
                <p className="text-sm">Open a file from the explorer to start editing</p>
              </div>
            </div>
          )}
        </div>

        {/* Bottom Panel */}
        {isBottomPanelOpen && (
          <div
            className="bg-gray-800 border-t border-gray-700"
            style={{ height: `${bottomPanelHeight}px` }}
          >
            {/* Bottom Panel Tabs */}
            <div className="flex items-center justify-between bg-gray-800 border-b border-gray-700">
              <div className="flex">
                {bottomPanelTabs.map(tab => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveBottomTab(tab.id)}
                      className={`flex items-center space-x-2 px-3 py-2 text-sm ${
                        activeBottomTab === tab.id
                          ? 'text-white border-b-2 border-blue-500'
                          : 'text-gray-400 hover:text-gray-300'
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span>{tab.name}</span>
                    </button>
                  );
                })}
              </div>
              <div className="flex items-center space-x-2 px-3">
                <button
                  onClick={() => setIsBottomPanelOpen(false)}
                  className="text-gray-400 hover:text-gray-300"
                >
                  <ChevronDownIcon className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Bottom Panel Content */}
            <div className="h-full overflow-hidden">
              {activeBottomTab === 'terminal' && <Terminal />}
              {activeBottomTab === 'problems' && (
                <div className="p-4 text-gray-400">
                  <p>No problems detected</p>
                </div>
              )}
              {activeBottomTab === 'output' && (
                <div className="p-4 text-gray-400">
                  <p>Output panel - logs and build results will appear here</p>
                </div>
              )}
              {activeBottomTab === 'debug-console' && (
                <div className="p-4 text-gray-400">
                  <p>Debug console - debugging output will appear here</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Show Bottom Panel Button (when collapsed) */}
        {!isBottomPanelOpen && (
          <div className="bg-gray-800 border-t border-gray-700 p-2">
            <button
              onClick={() => setIsBottomPanelOpen(true)}
              className="flex items-center space-x-2 text-gray-400 hover:text-gray-300 text-sm"
            >
              <ChevronUpIcon className="h-4 w-4" />
              <span>Show Panel</span>
            </button>
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="absolute bottom-0 left-0 right-0 bg-blue-600 text-white text-xs px-4 py-1 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <span>PoolotHost IDE</span>
          {selectedFile && (
            <>
              <span>•</span>
              <span>{selectedFile.name}</span>
              <span>•</span>
              <span>{getLanguageFromFilename(selectedFile.name)}</span>
            </>
          )}
        </div>
        <div className="flex items-center space-x-4">
          <span>UTF-8</span>
          <span>LF</span>
          <span className="text-green-300">● Ready</span>
        </div>
      </div>
    </div>
  );
};

export default DeveloperTools;
