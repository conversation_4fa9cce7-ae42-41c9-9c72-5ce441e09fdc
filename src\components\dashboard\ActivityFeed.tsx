import { 
  RocketLaunchIcon, 
  GlobeAltIcon, 
  ShieldCheckIcon, 
  ServerIcon,
  BellIcon,
} from '@heroicons/react/24/outline';
import Card from '../ui/Card';
import Button from '../ui/Button';

interface Activity {
  id: string;
  type: 'deployment' | 'domain' | 'ssl' | 'storage' | 'other';
  title: string;
  description: string;
  date: string;
}

interface ActivityFeedProps {
  activities: Activity[];
}

export default function ActivityFeed({ activities }: ActivityFeedProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'deployment':
        return (
          <div className="h-8 w-8 rounded-full bg-secondary-900/50 border border-secondary-500/30 flex items-center justify-center">
            <RocketLaunchIcon className="h-4 w-4 text-secondary-400" />
          </div>
        );
      case 'domain':
        return (
          <div className="h-8 w-8 rounded-full bg-blue-900/50 border border-blue-500/30 flex items-center justify-center">
            <GlobeAltIcon className="h-4 w-4 text-blue-400" />
          </div>
        );
      case 'ssl':
        return (
          <div className="h-8 w-8 rounded-full bg-green-900/50 border border-green-500/30 flex items-center justify-center">
            <ShieldCheckIcon className="h-4 w-4 text-green-400" />
          </div>
        );
      case 'storage':
        return (
          <div className="h-8 w-8 rounded-full bg-yellow-900/50 border border-yellow-500/30 flex items-center justify-center">
            <ServerIcon className="h-4 w-4 text-yellow-400" />
          </div>
        );
      default:
        return (
          <div className="h-8 w-8 rounded-full bg-gray-900/50 border border-gray-500/30 flex items-center justify-center">
            <BellIcon className="h-4 w-4 text-gray-400" />
          </div>
        );
    }
  };

  return (
    <Card className="p-6 relative overflow-hidden h-full">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-base font-semibold leading-6 text-white">Recent Activity</h3>
        <Button variant="ghost" size="sm">View All</Button>
      </div>
      
      {activities.length === 0 ? (
        <div className="text-center py-8">
          <BellIcon className="h-12 w-12 text-gray-500 mx-auto mb-3" />
          <p className="text-gray-400">No recent activity</p>
        </div>
      ) : (
        <div className="flow-root">
          <ul role="list" className="-mb-8">
            {activities.map((activity, activityIdx) => (
              <li key={activity.id}>
                <div className="relative pb-8">
                  {activityIdx !== activities.length - 1 ? (
                    <span
                      className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-white/10"
                      aria-hidden="true"
                    />
                  ) : null}
                  <div className="relative flex space-x-3 items-start">
                    <div className="relative">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm text-white font-medium">
                        {activity.title}
                      </div>
                      <p className="mt-0.5 text-xs text-gray-400">
                        {activity.description}
                      </p>
                      <p className="mt-2 text-xs text-gray-500">
                        {activity.date}
                      </p>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
      
      {/* Decorative elements */}
      <div className="absolute -z-10 -top-20 -right-20 w-64 h-64 bg-secondary-600/5 rounded-full blur-3xl"></div>
      <div className="absolute -z-10 -bottom-20 -left-20 w-64 h-64 bg-primary-600/5 rounded-full blur-3xl"></div>
    </Card>
  );
}
