import { useState } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  ServerIcon, 
  PlusIcon, 
  MagnifyingGlassIcon,
  ArrowPathIcon,
  PauseIcon,
  PlayIcon,
  CpuChipIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ClockIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

// Sample data for servers
const serversData = [
  { 
    id: '1', 
    name: 'us-east-1-web-01', 
    type: 'Web Server',
    status: 'running',
    location: 'US East (N. Virginia)',
    ip: '***********',
    cpu: '45%',
    memory: '62%',
    disk: '38%',
    uptime: '99.98%',
    lastRestart: '32 days ago',
  },
  { 
    id: '2', 
    name: 'eu-west-1-db-01', 
    type: 'Database',
    status: 'running',
    location: 'EU West (Ireland)',
    ip: '***********',
    cpu: '28%',
    memory: '75%',
    disk: '52%',
    uptime: '99.95%',
    lastRestart: '15 days ago',
  },
  { 
    id: '3', 
    name: 'us-west-2-cache-01', 
    type: 'Cache',
    status: 'running',
    location: 'US West (Oregon)',
    ip: '***********',
    cpu: '12%',
    memory: '45%',
    disk: '22%',
    uptime: '100%',
    lastRestart: '45 days ago',
  },
  { 
    id: '4', 
    name: 'ap-southeast-1-storage-01', 
    type: 'Storage',
    status: 'running',
    location: 'Asia Pacific (Singapore)',
    ip: '***********',
    cpu: '8%',
    memory: '32%',
    disk: '78%',
    uptime: '99.99%',
    lastRestart: '60 days ago',
  },
  { 
    id: '5', 
    name: 'us-east-1-backup-01', 
    type: 'Backup',
    status: 'maintenance',
    location: 'US East (N. Virginia)',
    ip: '***********',
    cpu: '0%',
    memory: '5%',
    disk: '65%',
    uptime: '98.5%',
    lastRestart: '2 days ago',
  },
  { 
    id: '6', 
    name: 'eu-central-1-web-02', 
    type: 'Web Server',
    status: 'warning',
    location: 'EU Central (Frankfurt)',
    ip: '***********',
    cpu: '92%',
    memory: '88%',
    disk: '45%',
    uptime: '99.7%',
    lastRestart: '10 days ago',
  },
  { 
    id: '7', 
    name: 'us-west-1-analytics-01', 
    type: 'Analytics',
    status: 'stopped',
    location: 'US West (N. California)',
    ip: '***********',
    cpu: '0%',
    memory: '0%',
    disk: '28%',
    uptime: '95.2%',
    lastRestart: '1 day ago',
  },
];

// Sample data for server alerts
const serverAlerts = [
  {
    id: '1',
    server: 'eu-central-1-web-02',
    message: 'High CPU usage (92%)',
    severity: 'high',
    time: '15 minutes ago',
  },
  {
    id: '2',
    server: 'ap-southeast-1-storage-01',
    message: 'Disk space approaching threshold (78%)',
    severity: 'medium',
    time: '2 hours ago',
  },
  {
    id: '3',
    server: 'eu-west-1-db-01',
    message: 'Memory usage above 70%',
    severity: 'medium',
    time: '4 hours ago',
  },
];

export default function Servers() {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [serversPerPage] = useState(5);
  
  // Filter servers based on search term, type, and status
  const filteredServers = serversData.filter(server => {
    const matchesSearch = 
      server.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      server.ip.toLowerCase().includes(searchTerm.toLowerCase()) ||
      server.location.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === 'all' || server.type.toLowerCase() === typeFilter.toLowerCase();
    const matchesStatus = statusFilter === 'all' || server.status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });
  
  // Pagination
  const indexOfLastServer = currentPage * serversPerPage;
  const indexOfFirstServer = indexOfLastServer - serversPerPage;
  const currentServers = filteredServers.slice(indexOfFirstServer, indexOfLastServer);
  const totalPages = Math.ceil(filteredServers.length / serversPerPage);
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <span className="h-1.5 w-1.5 rounded-full bg-green-400 mr-1.5 animate-pulse"></span>
            Running
          </span>
        );
      case 'stopped':
        return (
          <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
            <PauseIcon className="h-3 w-3 mr-1 text-gray-400" />
            Stopped
          </span>
        );
      case 'maintenance':
        return (
          <span className="inline-flex items-center rounded-full bg-blue-900/30 border border-blue-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-blue-300">
            <ArrowPathIcon className="h-3 w-3 mr-1 text-blue-400" />
            Maintenance
          </span>
        );
      case 'warning':
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-900/30 border border-yellow-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-yellow-300">
            <ExclamationTriangleIcon className="h-3 w-3 mr-1 text-yellow-400" />
            Warning
          </span>
        );
      default:
        return null;
    }
  };
  
  const getUsageIndicator = (value: string, type: string) => {
    const percentage = parseInt(value.replace('%', ''));
    let colorClass = 'bg-green-500';
    
    if (percentage > 80) {
      colorClass = 'bg-red-500';
    } else if (percentage > 60) {
      colorClass = 'bg-yellow-500';
    }
    
    return (
      <div className="flex items-center">
        <div className="w-16 h-2 bg-gray-700 rounded-full mr-2">
          <div 
            className={`h-full ${colorClass} rounded-full`}
            style={{ width: value }}
          ></div>
        </div>
        <span className="text-xs text-gray-300">{value}</span>
      </div>
    );
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Server Management</h1>
          <p className="mt-1 text-sm text-gray-400">Monitor and manage your server infrastructure</p>
        </div>
        <Button 
          variant="glass" 
          glow={true}
          icon={<PlusIcon className="h-5 w-5" />}
        >
          Add Server
        </Button>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Card className="p-6">
            <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
              <div className="relative flex-grow max-w-md">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  type="text"
                  className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10 
                    placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                    sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                  placeholder="Search servers by name, IP, or location"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
                <select
                  className="rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10 
                    focus:ring-2 focus:ring-inset focus:ring-red-500 
                    sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <option value="all">All Types</option>
                  <option value="web server">Web Server</option>
                  <option value="database">Database</option>
                  <option value="cache">Cache</option>
                  <option value="storage">Storage</option>
                  <option value="backup">Backup</option>
                  <option value="analytics">Analytics</option>
                </select>
                
                <select
                  className="rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10 
                    focus:ring-2 focus:ring-inset focus:ring-red-500 
                    sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="all">All Status</option>
                  <option value="running">Running</option>
                  <option value="stopped">Stopped</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="warning">Warning</option>
                </select>
              </div>
            </div>
            
            <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
              <table className="min-w-full divide-y divide-white/10">
                <thead>
                  <tr>
                    <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                      Server
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Status
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Location
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      CPU
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Memory
                    </th>
                    <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-white/10">
                  {currentServers.map((server) => (
                    <tr key={server.id} className="hover:bg-white/5 transition-colors duration-200">
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                        <div className="flex items-center">
                          <ServerIcon className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <div className="font-medium text-white">{server.name}</div>
                            <div className="text-gray-400">{server.type}</div>
                          </div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        {getStatusBadge(server.status)}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                        {server.location}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        {getUsageIndicator(server.cpu, 'cpu')}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        {getUsageIndicator(server.memory, 'memory')}
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <div className="flex justify-end space-x-2">
                          {server.status === 'running' ? (
                            <Button 
                              variant="outline" 
                              size="sm"
                              className="p-1"
                              title="Stop Server"
                            >
                              <PauseIcon className="h-4 w-4" />
                            </Button>
                          ) : (
                            <Button 
                              variant="outline" 
                              size="sm"
                              className="p-1"
                              title="Start Server"
                            >
                              <PlayIcon className="h-4 w-4" />
                            </Button>
                          )}
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="p-1"
                            title="Restart Server"
                          >
                            <ArrowPathIcon className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="glass" 
                            size="sm"
                          >
                            Manage
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between border-t border-white/10 bg-white/5 px-4 py-3 sm:px-6 mt-4 rounded-xl">
                <div className="flex flex-1 justify-between sm:hidden">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
                <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-400">
                      Showing <span className="font-medium text-white">{indexOfFirstServer + 1}</span> to{' '}
                      <span className="font-medium text-white">
                        {Math.min(indexOfLastServer, filteredServers.length)}
                      </span>{' '}
                      of <span className="font-medium text-white">{filteredServers.length}</span> results
                    </p>
                  </div>
                  <div>
                    <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-l-md"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      {Array.from({ length: totalPages }).map((_, index) => (
                        <Button
                          key={index}
                          variant={currentPage === index + 1 ? 'glass' : 'outline'}
                          size="sm"
                          className="px-4"
                          onClick={() => setCurrentPage(index + 1)}
                        >
                          {index + 1}
                        </Button>
                      ))}
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-r-md"
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
        
        <div className="lg:col-span-1">
          <Card className="p-6 relative overflow-hidden h-full">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Server Alerts</h3>
              <Button variant="outline" size="sm">View All</Button>
            </div>
            
            <div className="space-y-4">
              {serverAlerts.map((alert) => (
                <div 
                  key={alert.id} 
                  className={`p-4 rounded-lg border ${
                    alert.severity === 'high' ? 'border-red-500/30 bg-red-900/10' : 
                    alert.severity === 'medium' ? 'border-yellow-500/30 bg-yellow-900/10' : 
                    'border-blue-500/30 bg-blue-900/10'
                  }`}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <ExclamationTriangleIcon 
                        className={`h-5 w-5 ${
                          alert.severity === 'high' ? 'text-red-400' : 
                          alert.severity === 'medium' ? 'text-yellow-400' : 
                          'text-blue-400'
                        }`} 
                        aria-hidden="true" 
                      />
                    </div>
                    <div className="ml-3 flex-1">
                      <h3 className="text-sm font-medium text-white">{alert.server}</h3>
                      <div className="mt-1 text-xs text-gray-400">
                        <p>{alert.message}</p>
                        <p className="mt-1 text-gray-500">{alert.time}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-8">
              <h3 className="text-base font-semibold leading-6 text-white mb-4">Server Health</h3>
              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-white">Overall Uptime</span>
                    <span className="text-sm text-green-400">99.95%</span>
                  </div>
                  <div className="w-full h-2 bg-gray-700 rounded-full">
                    <div className="h-full bg-green-500 rounded-full" style={{ width: '99.95%' }}></div>
                  </div>
                </div>
                
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-white">Average CPU Usage</span>
                    <span className="text-sm text-yellow-400">42%</span>
                  </div>
                  <div className="w-full h-2 bg-gray-700 rounded-full">
                    <div className="h-full bg-yellow-500 rounded-full" style={{ width: '42%' }}></div>
                  </div>
                </div>
                
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-white">Average Memory Usage</span>
                    <span className="text-sm text-yellow-400">58%</span>
                  </div>
                  <div className="w-full h-2 bg-gray-700 rounded-full">
                    <div className="h-full bg-yellow-500 rounded-full" style={{ width: '58%' }}></div>
                  </div>
                </div>
                
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-white">Average Disk Usage</span>
                    <span className="text-sm text-green-400">47%</span>
                  </div>
                  <div className="w-full h-2 bg-gray-700 rounded-full">
                    <div className="h-full bg-green-500 rounded-full" style={{ width: '47%' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
