import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { 
  ClockIcon,
  PlusIcon,
  TrashIcon,
  ArrowPathIcon,
  PlayIcon,
  PauseIcon,
  DocumentTextIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

export default function ProjectJobs() {
  const project = useOutletContext<any>();
  const [isAddingJob, setIsAddingJob] = useState(false);
  const [jobName, setJobName] = useState('');
  const [command, setCommand] = useState('');
  const [schedule, setSchedule] = useState('0 0 * * *');
  const [isConfirmingDelete, setIsConfirmingDelete] = useState<string | null>(null);
  
  // Sample jobs data
  const jobs = [
    {
      id: 'job-1',
      name: 'Database Backup',
      command: 'pg_dump -U postgres -d myapp > /backup/db-$(date +%Y%m%d).sql',
      schedule: '0 0 * * *', // Daily at midnight
      lastRun: '6 hours ago',
      nextRun: 'in 18 hours',
      status: 'active',
      lastRunStatus: 'success',
    },
    {
      id: 'job-2',
      name: 'Cache Clear',
      command: 'rm -rf /tmp/cache/* && echo "Cache cleared"',
      schedule: '0 */6 * * *', // Every 6 hours
      lastRun: '2 hours ago',
      nextRun: 'in 4 hours',
      status: 'active',
      lastRunStatus: 'success',
    },
    {
      id: 'job-3',
      name: 'Log Rotation',
      command: 'find /var/log -name "*.log" -size +10M -exec gzip {} \\;',
      schedule: '0 12 * * 0', // Weekly on Sunday at noon
      lastRun: '4 days ago',
      nextRun: 'in 3 days',
      status: 'paused',
      lastRunStatus: 'failed',
    },
  ];
  
  // Function to add a new job
  const handleAddJob = () => {
    // In a real app, this would call an API to create a new job
    console.log('Adding job...');
    setIsAddingJob(false);
    setJobName('');
    setCommand('');
    setSchedule('0 0 * * *');
    // For demo purposes, we'll just show a success message
    alert('Job added successfully!');
  };
  
  // Function to delete a job
  const handleDeleteJob = (jobId: string) => {
    // In a real app, this would call an API to delete the job
    console.log(`Deleting job ${jobId}...`);
    setIsConfirmingDelete(null);
    // For demo purposes, we'll just show a success message
    alert('Job deleted successfully!');
  };
  
  // Function to toggle job status
  const toggleJobStatus = (jobId: string, currentStatus: string) => {
    // In a real app, this would call an API to update the job status
    console.log(`Toggling job ${jobId} status from ${currentStatus} to ${currentStatus === 'active' ? 'paused' : 'active'}...`);
    // For demo purposes, we'll just show a success message
    alert(`Job ${currentStatus === 'active' ? 'paused' : 'activated'} successfully!`);
  };
  
  // Function to run a job immediately
  const runJobNow = (jobId: string) => {
    // In a real app, this would call an API to run the job immediately
    console.log(`Running job ${jobId} now...`);
    // For demo purposes, we'll just show a success message
    alert('Job started successfully!');
  };
  
  // Function to get status badge
  const getStatusBadge = (status: string) => {
    if (status === 'active') {
      return (
        <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
          <CheckCircleIcon className="h-3 w-3 mr-1 text-green-400" />
          Active
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
          <PauseIcon className="h-3 w-3 mr-1 text-gray-400" />
          Paused
        </span>
      );
    }
  };
  
  // Function to get last run status badge
  const getLastRunStatusBadge = (status: string) => {
    if (status === 'success') {
      return (
        <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
          <CheckCircleIcon className="h-3 w-3 mr-1 text-green-400" />
          Success
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
          <XCircleIcon className="h-3 w-3 mr-1 text-red-400" />
          Failed
        </span>
      );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-white">Scheduled Jobs</h2>
          <p className="text-sm text-gray-400">Manage recurring tasks and cron jobs</p>
        </div>
        <Button 
          variant="glass" 
          size="sm"
          icon={<PlusIcon className="h-4 w-4" />}
          onClick={() => setIsAddingJob(true)}
        >
          Add Job
        </Button>
      </div>
      
      {isAddingJob && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Add New Scheduled Job</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Job Name
              </label>
              <input
                type="text"
                className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                  placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                placeholder="e.g., Database Backup"
                value={jobName}
                onChange={(e) => setJobName(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Command
              </label>
              <textarea
                rows={3}
                className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                  placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                placeholder="e.g., pg_dump -U postgres -d myapp > /backup/db-$(date +%Y%m%d).sql"
                value={command}
                onChange={(e) => setCommand(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Schedule (Cron Expression)
              </label>
              <input
                type="text"
                className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                  placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                placeholder="e.g., 0 0 * * * (daily at midnight)"
                value={schedule}
                onChange={(e) => setSchedule(e.target.value)}
              />
              <p className="mt-1 text-xs text-gray-500">Format: minute hour day-of-month month day-of-week</p>
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setIsAddingJob(false)}
              >
                Cancel
              </Button>
              <Button 
                variant="glass" 
                size="sm"
                icon={<PlusIcon className="h-4 w-4" />}
                onClick={handleAddJob}
                disabled={!jobName || !command || !schedule}
              >
                Add Job
              </Button>
            </div>
          </div>
        </Card>
      )}
      
      <div className="space-y-4">
        {jobs.map((job) => (
          <Card key={job.id} className="p-6">
            <div className="flex flex-col md:flex-row justify-between gap-4">
              <div className="flex-grow">
                <div className="flex items-center mb-2">
                  {getStatusBadge(job.status)}
                  <h3 className="text-lg font-semibold text-white ml-2">{job.name}</h3>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-300">
                    <ClockIcon className="h-4 w-4 mr-2 text-gray-400" />
                    Schedule: <code className="ml-1 px-1 py-0.5 rounded bg-white/10 text-xs">{job.schedule}</code>
                  </div>
                  <div className="flex items-center text-sm text-gray-300">
                    <CalendarIcon className="h-4 w-4 mr-2 text-gray-400" />
                    Last run: {job.lastRun}
                    <span className="mx-2 text-gray-500">•</span>
                    Next run: {job.nextRun}
                  </div>
                  <div className="flex items-center text-sm text-gray-300">
                    <DocumentTextIcon className="h-4 w-4 mr-2 text-gray-400" />
                    Command: <code className="ml-1 px-1 py-0.5 rounded bg-white/10 text-xs truncate max-w-md">{job.command}</code>
                  </div>
                  <div className="flex items-center text-sm text-gray-300">
                    Last run status: {getLastRunStatusBadge(job.lastRunStatus)}
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap gap-2 items-start">
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={<ArrowPathIcon className="h-4 w-4" />}
                  onClick={() => runJobNow(job.id)}
                >
                  Run Now
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={job.status === 'active' ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
                  onClick={() => toggleJobStatus(job.id, job.status)}
                >
                  {job.status === 'active' ? 'Pause' : 'Activate'}
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={<DocumentTextIcon className="h-4 w-4" />}
                >
                  View Logs
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={<TrashIcon className="h-4 w-4" />}
                  onClick={() => setIsConfirmingDelete(job.id)}
                >
                  Delete
                </Button>
              </div>
            </div>
            
            {isConfirmingDelete === job.id && (
              <div className="mt-4 p-3 rounded-lg bg-red-900/30 border border-red-500/30">
                <div className="flex items-start">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-white mb-2">Are you sure you want to delete this job?</p>
                    <p className="text-xs text-gray-300 mb-3">This action cannot be undone. The job will be permanently removed from the schedule.</p>
                    <div className="flex space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsConfirmingDelete(null)}
                      >
                        Cancel
                      </Button>
                      <Button 
                        variant="glass" 
                        size="sm"
                        icon={<TrashIcon className="h-4 w-4" />}
                        onClick={() => handleDeleteJob(job.id)}
                      >
                        Confirm Delete
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>
      
      {jobs.length === 0 && (
        <Card className="p-8 text-center">
          <ClockIcon className="h-12 w-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">No Scheduled Jobs</h3>
          <p className="text-gray-400 mb-6">Create scheduled jobs to automate recurring tasks for your application.</p>
          <Button 
            variant="glass" 
            icon={<PlusIcon className="h-4 w-4" />}
            onClick={() => setIsAddingJob(true)}
          >
            Add First Job
          </Button>
        </Card>
      )}
      
      <Card className="p-6">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-secondary-400 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h3 className="text-base font-semibold text-white mb-2">About Scheduled Jobs</h3>
            <p className="text-sm text-gray-300 mb-2">
              Scheduled jobs allow you to automate recurring tasks using cron expressions. Common use cases include:
            </p>
            <ul className="list-disc list-inside text-sm text-gray-300 space-y-1">
              <li>Database backups</li>
              <li>Cache clearing</li>
              <li>Report generation</li>
              <li>Data synchronization</li>
              <li>Log rotation and cleanup</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
}
