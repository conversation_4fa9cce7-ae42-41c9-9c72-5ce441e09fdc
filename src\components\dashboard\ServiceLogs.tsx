import { useState } from 'react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import {
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

interface Log {
  timestamp: string;
  level: string;
  message: string;
}

interface ServiceLogsProps {
  logs: Log[];
}

export default function ServiceLogs({ logs }: ServiceLogsProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState('all');
  const [autoRefresh, setAutoRefresh] = useState(false);

  // Filter logs based on search term and level
  const filteredLogs = Array.isArray(logs)
    ? logs.filter(log => {
        const matchesSearch =
          log.message.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesLevel = levelFilter === 'all' || log.level === levelFilter;

        return matchesSearch && matchesLevel;
      })
    : [];

  const getLogLevelIndicator = (level: string) => {
    switch (level) {
      case 'error':
        return <span className="h-2 w-2 rounded-full bg-red-500 mr-2"></span>;
      case 'warn':
        return <span className="h-2 w-2 rounded-full bg-yellow-500 mr-2"></span>;
      case 'info':
        return <span className="h-2 w-2 rounded-full bg-blue-500 mr-2"></span>;
      case 'debug':
        return <span className="h-2 w-2 rounded-full bg-gray-500 mr-2"></span>;
      default:
        return <span className="h-2 w-2 rounded-full bg-gray-500 mr-2"></span>;
    }
  };

  return (
    <Card className="p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h2 className="text-lg font-semibold text-white">Service Logs</h2>
          <p className="text-sm text-gray-400">View real-time logs for your service</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            icon={<ArrowDownTrayIcon className="h-4 w-4" />}
          >
            Download
          </Button>
          <Button
            variant={autoRefresh ? "glass" : "outline"}
            size="sm"
            icon={<ArrowPathIcon className={`h-4 w-4 ${autoRefresh ? 'animate-spin' : ''}`} />}
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? 'Auto-refreshing' : 'Refresh'}
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row justify-between gap-4 mb-4">
        <div className="relative flex-grow max-w-md">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10
              placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
              sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
            placeholder="Search logs"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex space-x-2">
          <select
            className="rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10
              focus:ring-2 focus:ring-inset focus:ring-secondary-500
              sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
            value={levelFilter}
            onChange={(e) => setLevelFilter(e.target.value)}
          >
            <option value="all">All Levels</option>
            <option value="error">Error</option>
            <option value="warn">Warning</option>
            <option value="info">Info</option>
            <option value="debug">Debug</option>
          </select>

          <select
            className="rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10
              focus:ring-2 focus:ring-inset focus:ring-secondary-500
              sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
            defaultValue="1h"
          >
            <option value="15m">Last 15 minutes</option>
            <option value="1h">Last hour</option>
            <option value="6h">Last 6 hours</option>
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
          </select>
        </div>
      </div>

      <div className="mt-4 relative">
        <div className="absolute top-0 right-0 p-2 text-xs text-gray-400 flex items-center">
          <ClockIcon className="h-3 w-3 mr-1" />
          <span>May 17, 10:11 PM</span>
          <span className="ml-2 inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2 py-0.5 text-xs font-medium text-green-300">
            Live
          </span>
        </div>
        <div className="overflow-hidden rounded-xl border border-white/10 bg-gray-900 text-white font-mono text-sm">
          <div className="p-4 max-h-[600px] overflow-y-auto">
            {filteredLogs.length > 0 ? (
              filteredLogs.map((log, index) => (
                <div key={index} className="py-1 flex">
                  <span className="text-gray-500 mr-4">{log.timestamp}</span>
                  <div className="flex items-center">
                    {getLogLevelIndicator(log.level)}
                    <span className={`
                      ${log.level === 'error' ? 'text-red-400' : ''}
                      ${log.level === 'warn' ? 'text-yellow-400' : ''}
                      ${log.level === 'info' ? 'text-blue-400' : ''}
                      ${log.level === 'debug' ? 'text-gray-400' : ''}
                    `}>
                      {log.message}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="py-4 text-center text-gray-400">
                No logs found matching your filters.
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
}
