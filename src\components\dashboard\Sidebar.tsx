import { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Link, useLocation } from 'react-router-dom';
import {
  XMarkIcon,
  HomeIcon,
  ServerIcon,
  GlobeAltIcon,
  CreditCardIcon,
  Cog6ToothIcon,
  UserIcon,
  ArrowRightOnRectangleIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Services', href: '/dashboard/services', icon: ServerIcon },
  { name: 'Hosting', href: '/dashboard/hosting', icon: ServerIcon },
  { name: 'Domains', href: '/dashboard/domains', icon: GlobeAltIcon },
  { name: 'Billing', href: '/dashboard/billing', icon: CreditCardIcon },
  { name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },
  { name: 'Settings', href: '/dashboard/settings', icon: Cog6ToothIcon },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

interface SidebarProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

export default function Sidebar({ sidebarOpen, setSidebarOpen }: SidebarProps) {
  const location = useLocation();

  return (
    <>
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setSidebarOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5 text-white hover:bg-white/10 rounded-md transition-colors duration-200"
                      onClick={() => setSidebarOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                {/* Mobile sidebar */}
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900/95 backdrop-blur-xl px-6 pb-4 border-r border-white/10">
                  <div className="flex h-16 shrink-0 items-center">
                    <Link to="/" className="flex items-center transition-transform duration-300 hover:scale-105">
                      <ServerIcon className="h-8 w-auto text-secondary-500" />
                      <span className="ml-2 text-xl font-bold text-white">PoolotHost</span>
                    </Link>
                  </div>
                  <nav className="flex flex-1 flex-col">
                    <ul role="list" className="flex flex-1 flex-col gap-y-7">
                      <li>
                        <ul role="list" className="-mx-2 space-y-1">
                          {navigation.map((item, index) => (
                            <li key={item.name} style={{ animationDelay: `${index * 50}ms` }} className="animate-fade-in">
                              <Link
                                to={item.href}
                                className={classNames(
                                  location.pathname === item.href
                                    ? 'bg-white/10 text-white border border-white/10'
                                    : 'text-gray-300 hover:text-white hover:bg-white/5',
                                  'group flex gap-x-3 rounded-xl p-2 text-sm leading-6 font-medium transition-all duration-200'
                                )}
                                onClick={() => setSidebarOpen(false)}
                              >
                                <item.icon
                                  className={classNames(
                                    location.pathname === item.href ? 'text-secondary-400' : 'text-gray-400 group-hover:text-secondary-400',
                                    'h-6 w-6 shrink-0 transition-colors duration-200'
                                  )}
                                  aria-hidden="true"
                                />
                                {item.name}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </li>
                      <li className="mt-auto space-y-1">
                        <Link
                          to="/dashboard/profile"
                          className="group -mx-2 flex gap-x-3 rounded-xl p-2 text-sm font-medium leading-6 text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-200"
                          onClick={() => setSidebarOpen(false)}
                        >
                          <UserIcon
                            className="h-6 w-6 shrink-0 text-gray-400 group-hover:text-secondary-400 transition-colors duration-200"
                            aria-hidden="true"
                          />
                          Profile
                        </Link>
                        <Link
                          to="/login"
                          className="group -mx-2 flex gap-x-3 rounded-xl p-2 text-sm font-medium leading-6 text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-200"
                          onClick={() => setSidebarOpen(false)}
                        >
                          <ArrowRightOnRectangleIcon
                            className="h-6 w-6 shrink-0 text-gray-400 group-hover:text-secondary-400 transition-colors duration-200"
                            aria-hidden="true"
                          />
                          Sign out
                        </Link>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:w-72 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900/50 backdrop-blur-md border-r border-white/10 px-6 pb-4">
          <div className="flex h-16 shrink-0 items-center">
            <Link to="/" className="flex items-center transition-transform duration-300 hover:scale-105">
              <ServerIcon className="h-8 w-auto text-secondary-500" />
              <span className="ml-2 text-xl font-bold text-white">PoolotHost</span>
            </Link>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item, index) => (
                    <li key={item.name} style={{ animationDelay: `${index * 50}ms` }} className="animate-fade-in">
                      <Link
                        to={item.href}
                        className={classNames(
                          location.pathname === item.href
                            ? 'bg-white/10 text-white border border-white/10'
                            : 'text-gray-300 hover:text-white hover:bg-white/5',
                          'group flex gap-x-3 rounded-xl p-2 text-sm leading-6 font-medium transition-all duration-200'
                        )}
                      >
                        <item.icon
                          className={classNames(
                            location.pathname === item.href ? 'text-secondary-400' : 'text-gray-400 group-hover:text-secondary-400',
                            'h-6 w-6 shrink-0 transition-colors duration-200'
                          )}
                          aria-hidden="true"
                        />
                        {item.name}
                        {location.pathname === item.href && (
                          <span className="absolute left-0 w-1 h-8 bg-secondary-500 rounded-r-full transform -translate-y-1"></span>
                        )}
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>
              <li className="mt-auto space-y-1">
                <Link
                  to="/dashboard/profile"
                  className="group -mx-2 flex gap-x-3 rounded-xl p-2 text-sm font-medium leading-6 text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-200"
                >
                  <UserIcon
                    className="h-6 w-6 shrink-0 text-gray-400 group-hover:text-secondary-400 transition-colors duration-200"
                    aria-hidden="true"
                  />
                  Profile
                </Link>
                <Link
                  to="/login"
                  className="group -mx-2 flex gap-x-3 rounded-xl p-2 text-sm font-medium leading-6 text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-200"
                >
                  <ArrowRightOnRectangleIcon
                    className="h-6 w-6 shrink-0 text-gray-400 group-hover:text-secondary-400 transition-colors duration-200"
                    aria-hidden="true"
                  />
                  Sign out
                </Link>
              </li>
            </ul>
          </nav>

          {/* Decorative elements */}
          <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-secondary-900/20 to-transparent pointer-events-none"></div>
        </div>
      </div>
    </>
  );
}
