import React, { useState, useRef } from 'react';
import {
  ServerIcon,
  PlusIcon,
  TrashIcon,
  PencilIcon,
  PlayIcon,
  StopIcon,
  KeyIcon,
  EyeIcon,
  EyeSlashIcon,
  ClipboardDocumentIcon,
  WifiIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface SSHConnection {
  id: string;
  name: string;
  host: string;
  port: number;
  username: string;
  password?: string;
  privateKey?: string;
  isConnected: boolean;
  lastConnected?: Date;
}

interface SSHManagerProps {
  onConnect: (connection: SSHConnection) => void;
}

const SSHManager: React.FC<SSHManagerProps> = ({ onConnect }) => {
  const [connections, setConnections] = useState<SSHConnection[]>([
    {
      id: '1',
      name: 'Production Server',
      host: 'prod.example.com',
      port: 22,
      username: 'root',
      isConnected: false,
      lastConnected: new Date(Date.now() - 86400000) // 1 day ago
    },
    {
      id: '2',
      name: 'Development Server',
      host: 'dev.example.com',
      port: 22,
      username: 'developer',
      isConnected: false,
      lastConnected: new Date(Date.now() - 3600000) // 1 hour ago
    }
  ]);

  const [showForm, setShowForm] = useState(false);
  const [editingConnection, setEditingConnection] = useState<SSHConnection | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    host: '',
    port: 22,
    username: '',
    password: '',
    privateKey: ''
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const newConnection: SSHConnection = {
      id: editingConnection?.id || Date.now().toString(),
      name: formData.name,
      host: formData.host,
      port: formData.port,
      username: formData.username,
      password: formData.password || undefined,
      privateKey: formData.privateKey || undefined,
      isConnected: false
    };

    if (editingConnection) {
      setConnections(prev => prev.map(conn => 
        conn.id === editingConnection.id ? newConnection : conn
      ));
    } else {
      setConnections(prev => [...prev, newConnection]);
    }

    resetForm();
  };

  const resetForm = () => {
    setFormData({
      name: '',
      host: '',
      port: 22,
      username: '',
      password: '',
      privateKey: ''
    });
    setEditingConnection(null);
    setShowForm(false);
    setShowPassword(false);
  };

  const editConnection = (connection: SSHConnection) => {
    setFormData({
      name: connection.name,
      host: connection.host,
      port: connection.port,
      username: connection.username,
      password: connection.password || '',
      privateKey: connection.privateKey || ''
    });
    setEditingConnection(connection);
    setShowForm(true);
  };

  const deleteConnection = (id: string) => {
    setConnections(prev => prev.filter(conn => conn.id !== id));
  };

  const connectToServer = (connection: SSHConnection) => {
    // Update connection status
    setConnections(prev => prev.map(conn => 
      conn.id === connection.id 
        ? { ...conn, isConnected: true, lastConnected: new Date() }
        : conn
    ));
    
    // Call parent handler
    onConnect(connection);
  };

  const disconnectFromServer = (id: string) => {
    setConnections(prev => prev.map(conn => 
      conn.id === id ? { ...conn, isConnected: false } : conn
    ));
  };

  const handleKeyFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setFormData(prev => ({ ...prev, privateKey: event.target?.result as string }));
      };
      reader.readAsText(file);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatLastConnected = (date?: Date) => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-800">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <ServerIcon className="h-5 w-5 text-blue-500" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">SSH Connections</h3>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center space-x-1 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Connection</span>
        </button>
      </div>

      {/* Connection Form */}
      {showForm && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <form onSubmit={handleSubmit} className="space-y-3">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                {editingConnection ? 'Edit Connection' : 'New SSH Connection'}
              </h4>
              <button
                type="button"
                onClick={resetForm}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <input
                type="text"
                placeholder="Connection Name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                required
              />
              <input
                type="text"
                placeholder="Host/IP Address"
                value={formData.host}
                onChange={(e) => setFormData(prev => ({ ...prev, host: e.target.value }))}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <input
                type="number"
                placeholder="Port"
                value={formData.port}
                onChange={(e) => setFormData(prev => ({ ...prev, port: parseInt(e.target.value) || 22 }))}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                min="1"
                max="65535"
                required
              />
              <input
                type="text"
                placeholder="Username"
                value={formData.username}
                onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                required
              />
            </div>

            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                placeholder="Password (optional if using key)"
                value={formData.password}
                onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                {showPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
              </button>
            </div>

            <div>
              <div className="flex items-center space-x-2 mb-2">
                <KeyIcon className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-700 dark:text-gray-300">Private Key (optional)</span>
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="text-xs text-blue-500 hover:text-blue-600"
                >
                  Upload File
                </button>
              </div>
              <textarea
                placeholder="Paste private key content here..."
                value={formData.privateKey}
                onChange={(e) => setFormData(prev => ({ ...prev, privateKey: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                rows={3}
              />
              <input
                ref={fileInputRef}
                type="file"
                accept=".pem,.key,.ppk"
                onChange={handleKeyFileUpload}
                className="hidden"
              />
            </div>

            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={resetForm}
                className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
              >
                {editingConnection ? 'Update' : 'Add'} Connection
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Connections List */}
      <div className="flex-1 overflow-auto">
        {connections.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <ServerIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No SSH Connections</h3>
              <p className="text-sm mb-4">Add your first SSH connection to get started</p>
              <button
                onClick={() => setShowForm(true)}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Add Connection
              </button>
            </div>
          </div>
        ) : (
          <div className="p-3 space-y-2">
            {connections.map(connection => (
              <div
                key={connection.id}
                className={`p-3 border rounded-lg ${
                  connection.isConnected
                    ? 'border-green-300 bg-green-50 dark:border-green-700 dark:bg-green-900/20'
                    : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded ${
                      connection.isConnected ? 'bg-green-100 dark:bg-green-800' : 'bg-gray-100 dark:bg-gray-700'
                    }`}>
                      {connection.isConnected ? (
                        <WifiIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
                      ) : (
                        <ServerIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                      )}
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                        {connection.name}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {connection.username}@{connection.host}:{connection.port}
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500">
                        Last connected: {formatLastConnected(connection.lastConnected)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => copyToClipboard(`${connection.username}@${connection.host}`)}
                      className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                      title="Copy connection string"
                    >
                      <ClipboardDocumentIcon className="h-4 w-4" />
                    </button>
                    
                    <button
                      onClick={() => editConnection(connection)}
                      className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                      title="Edit connection"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    
                    <button
                      onClick={() => deleteConnection(connection.id)}
                      className="p-1 text-red-500 hover:text-red-700"
                      title="Delete connection"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>

                    {connection.isConnected ? (
                      <button
                        onClick={() => disconnectFromServer(connection.id)}
                        className="flex items-center space-x-1 px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                      >
                        <StopIcon className="h-4 w-4" />
                        <span>Disconnect</span>
                      </button>
                    ) : (
                      <button
                        onClick={() => connectToServer(connection)}
                        className="flex items-center space-x-1 px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
                      >
                        <PlayIcon className="h-4 w-4" />
                        <span>Connect</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Quick Guide */}
      <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
        <p className="text-xs text-gray-600 dark:text-gray-400">
          💡 <strong>Quick Guide:</strong> Add SSH connections, use password or private key authentication, connect to open terminal
        </p>
      </div>
    </div>
  );
};

export default SSHManager;
