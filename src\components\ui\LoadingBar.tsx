import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

export default function LoadingBar() {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const location = useLocation();

  useEffect(() => {
    // Start loading animation when location changes
    setProgress(0);
    setIsVisible(true);

    // Simulate progress
    const timer1 = setTimeout(() => setProgress(30), 100);
    const timer2 = setTimeout(() => setProgress(60), 300);
    const timer3 = setTimeout(() => setProgress(80), 600);
    const timer4 = setTimeout(() => setProgress(90), 900);
    
    // Complete loading and hide the bar
    const timer5 = setTimeout(() => {
      setProgress(100);
      
      // Hide the bar after it reaches 100%
      const hideTimer = setTimeout(() => {
        setIsVisible(false);
      }, 200);
      
      return () => clearTimeout(hideTimer);
    }, 1000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      clearTimeout(timer4);
      clearTimeout(timer5);
    };
  }, [location]);

  if (!isVisible && progress === 0) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50 h-0.5 bg-gray-800">
      <div 
        className="h-full bg-gradient-to-r from-secondary-500 to-primary-500 transition-all duration-300 ease-out"
        style={{ 
          width: `${progress}%`,
          opacity: progress === 100 ? 0 : 1,
        }}
      />
    </div>
  );
}
