import { useState, useEffect } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  ChartBarIcon, 
  ArrowPathIcon,
  CalendarIcon,
  GlobeAltIcon,
  DevicePhoneMobileIcon,
  UserGroupIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  BarElement,
  ArcElement,
  Title, 
  Tooltip, 
  Legend, 
  Filler
);

// Sample data for analytics
const visitorStats = [
  { name: 'Total Visitors', value: '24,521', change: '12%', changeType: 'increase' },
  { name: 'Unique Visitors', value: '18,432', change: '8%', changeType: 'increase' },
  { name: 'Page Views', value: '87,291', change: '15%', changeType: 'increase' },
  { name: 'Bounce Rate', value: '32%', change: '3%', changeType: 'decrease' },
];

// Sample data for traffic sources
const trafficSources = [
  { name: 'Direct', value: 35 },
  { name: 'Organic Search', value: 25 },
  { name: 'Referral', value: 20 },
  { name: 'Social Media', value: 15 },
  { name: 'Email', value: 5 },
];

// Sample data for visitor demographics
const visitorDemographics = {
  countries: [
    { name: 'United States', value: 45 },
    { name: 'United Kingdom', value: 15 },
    { name: 'Germany', value: 10 },
    { name: 'Canada', value: 8 },
    { name: 'Australia', value: 7 },
    { name: 'Other', value: 15 },
  ],
  devices: [
    { name: 'Desktop', value: 55 },
    { name: 'Mobile', value: 35 },
    { name: 'Tablet', value: 10 },
  ],
};

export default function Analytics() {
  const [dateRange, setDateRange] = useState('7d');
  const [animatedData, setAnimatedData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
  
  // Real data for the chart
  const finalData = [1250, 1800, 1600, 2100, 1800, 2400, 2200];
  
  // Animate the chart data on component mount
  useEffect(() => {
    const animationDuration = 1500; // ms
    const steps = 30;
    const stepDuration = animationDuration / steps;
    
    let currentStep = 0;
    
    const interval = setInterval(() => {
      if (currentStep >= steps) {
        clearInterval(interval);
        setAnimatedData(finalData);
        return;
      }
      
      const progress = (currentStep + 1) / steps;
      const newData = finalData.map((value) => Math.round(value * progress));
      
      setAnimatedData(newData);
      currentStep++;
    }, stepDuration);
    
    return () => clearInterval(interval);
  }, []);

  // Visitor trend chart data
  const visitorTrendData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'Visitors',
        data: animatedData,
        fill: true,
        backgroundColor: 'rgba(139, 92, 246, 0.2)',
        borderColor: 'rgba(139, 92, 246, 1)',
        borderWidth: 2,
        tension: 0.4,
        pointBackgroundColor: 'rgba(139, 92, 246, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(139, 92, 246, 1)',
        pointRadius: 4,
      },
    ],
  };

  // Traffic sources chart data
  const trafficSourcesData = {
    labels: trafficSources.map(source => source.name),
    datasets: [
      {
        data: trafficSources.map(source => source.value),
        backgroundColor: [
          'rgba(139, 92, 246, 0.7)',
          'rgba(14, 165, 233, 0.7)',
          'rgba(249, 115, 22, 0.7)',
          'rgba(236, 72, 153, 0.7)',
          'rgba(34, 197, 94, 0.7)',
        ],
        borderColor: [
          'rgba(139, 92, 246, 1)',
          'rgba(14, 165, 233, 1)',
          'rgba(249, 115, 22, 1)',
          'rgba(236, 72, 153, 1)',
          'rgba(34, 197, 94, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Device breakdown chart data
  const deviceBreakdownData = {
    labels: visitorDemographics.devices.map(device => device.name),
    datasets: [
      {
        data: visitorDemographics.devices.map(device => device.value),
        backgroundColor: [
          'rgba(139, 92, 246, 0.7)',
          'rgba(14, 165, 233, 0.7)',
          'rgba(249, 115, 22, 0.7)',
        ],
        borderColor: [
          'rgba(139, 92, 246, 1)',
          'rgba(14, 165, 233, 1)',
          'rgba(249, 115, 22, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Chart options
  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        titleFont: {
          family: 'Poppins',
          size: 13,
        },
        bodyFont: {
          family: 'Poppins',
          size: 12,
        },
        padding: 12,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
    },
    animation: {
      duration: 1000,
      easing: 'easeOutQuart',
    },
  };

  const doughnutChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
          padding: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        titleFont: {
          family: 'Poppins',
          size: 13,
        },
        bodyFont: {
          family: 'Poppins',
          size: 12,
        },
        padding: 12,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      },
    },
    cutout: '70%',
    animation: {
      animateRotate: true,
      animateScale: true,
    },
  };

  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ');
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Analytics</h1>
          <p className="mt-1 text-sm text-gray-400">Track your website performance and visitor insights</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant={dateRange === '7d' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setDateRange('7d')}
          >
            7 Days
          </Button>
          <Button 
            variant={dateRange === '30d' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setDateRange('30d')}
          >
            30 Days
          </Button>
          <Button 
            variant={dateRange === '90d' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setDateRange('90d')}
          >
            90 Days
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            icon={<CalendarIcon className="h-4 w-4" />}
          >
            Custom
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {visitorStats.map((stat, index) => (
          <Card 
            key={stat.name} 
            className="p-6 relative overflow-hidden"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 rounded-full bg-secondary-900/30 border border-secondary-500/30 flex items-center justify-center">
                  <ChartBarIcon className="h-6 w-6 text-secondary-400" aria-hidden="true" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-300 truncate">{stat.name}</dt>
                  <dd>
                    <div className="text-lg font-semibold text-white">{stat.value}</div>
                  </dd>
                </dl>
              </div>
            </div>
            <div className="absolute bottom-0 inset-x-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/10 to-secondary-500/0 h-0.5"></div>
            
            <div className="absolute top-6 right-6">
              <div
                className={classNames(
                  stat.changeType === 'increase' ? 'bg-green-900/50 text-green-300 border border-green-500/30' : 'bg-red-900/50 text-red-300 border border-red-500/30',
                  'inline-flex items-baseline rounded-full px-2.5 py-0.5 text-sm font-medium backdrop-blur-sm'
                )}
              >
                {stat.changeType === 'increase' ? (
                  <ArrowUpIcon
                    className="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center text-green-400"
                    aria-hidden="true"
                  />
                ) : (
                  <ArrowDownIcon
                    className="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center text-red-400"
                    aria-hidden="true"
                  />
                )}
                {stat.change}
              </div>
            </div>
          </Card>
        ))}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Visitor Trends</h3>
              <Button 
                variant="outline" 
                size="sm"
                icon={<ArrowPathIcon className="h-4 w-4" />}
              >
                Refresh
              </Button>
            </div>
            <div className="h-80 relative">
              <Line data={visitorTrendData} options={lineChartOptions} />
            </div>
          </Card>
        </div>
        
        <div className="lg:col-span-1">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Traffic Sources</h3>
              <Button 
                variant="outline" 
                size="sm"
                icon={<ArrowPathIcon className="h-4 w-4" />}
              >
                Refresh
              </Button>
            </div>
            <div className="h-80 relative">
              <Doughnut data={trafficSourcesData} options={doughnutChartOptions} />
            </div>
          </Card>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Device Breakdown</h3>
              <Button 
                variant="outline" 
                size="sm"
                icon={<ArrowPathIcon className="h-4 w-4" />}
              >
                Refresh
              </Button>
            </div>
            <div className="h-64 relative">
              <Doughnut data={deviceBreakdownData} options={doughnutChartOptions} />
            </div>
            <div className="mt-6 grid grid-cols-3 gap-4">
              {visitorDemographics.devices.map((device, index) => (
                <div key={device.name} className="text-center">
                  <div className="text-xs text-gray-400">{device.name}</div>
                  <div className="text-lg font-semibold text-white">{device.value}%</div>
                </div>
              ))}
            </div>
          </Card>
        </div>
        
        <div className="lg:col-span-2">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Top Countries</h3>
              <Button 
                variant="outline" 
                size="sm"
                icon={<GlobeAltIcon className="h-4 w-4" />}
              >
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {visitorDemographics.countries.map((country) => (
                <div key={country.name} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center mr-3">
                      <GlobeAltIcon className="h-4 w-4 text-secondary-400" />
                    </div>
                    <span className="text-sm text-white">{country.name}</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-32 h-2 bg-gray-700 rounded-full mr-3">
                      <div 
                        className="h-full bg-secondary-500 rounded-full"
                        style={{ width: `${country.value}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-white">{country.value}%</span>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
