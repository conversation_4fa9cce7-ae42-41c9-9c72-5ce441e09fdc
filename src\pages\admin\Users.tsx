import { useState } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  ArrowPathIcon,
  EnvelopeIcon,
  LockClosedIcon,
} from '@heroicons/react/24/outline';

// Sample data for users
const usersData = [
  { 
    id: '1', 
    name: '<PERSON>', 
    email: '<EMAIL>', 
    role: 'Admin', 
    status: 'active',
    plan: 'Enterprise',
    lastLogin: '2 hours ago',
    created: 'Jan 12, 2023',
  },
  { 
    id: '2', 
    name: '<PERSON>', 
    email: '<EMAIL>', 
    role: 'User', 
    status: 'active',
    plan: 'Basic',
    lastLogin: '1 day ago',
    created: 'Mar 5, 2023',
  },
  { 
    id: '3', 
    name: '<PERSON><PERSON>', 
    email: '<EMAIL>', 
    role: 'User', 
    status: 'active',
    plan: 'Pro',
    lastLogin: '3 days ago',
    created: 'Feb 18, 2023',
  },
  { 
    id: '4', 
    name: '<PERSON>', 
    email: '<EMAIL>', 
    role: 'User', 
    status: 'inactive',
    plan: 'Pro',
    lastLogin: '2 weeks ago',
    created: 'Nov 30, 2022',
  },
  { 
    id: '5', 
    name: 'Courtney Henry', 
    email: '<EMAIL>', 
    role: 'User', 
    status: 'active',
    plan: 'Basic',
    lastLogin: '5 hours ago',
    created: 'Apr 22, 2023',
  },
  { 
    id: '6', 
    name: 'Tom Cook', 
    email: '<EMAIL>', 
    role: 'Admin', 
    status: 'active',
    plan: 'Enterprise',
    lastLogin: '1 hour ago',
    created: 'Dec 5, 2022',
  },
  { 
    id: '7', 
    name: 'Whitney Francis', 
    email: '<EMAIL>', 
    role: 'User', 
    status: 'suspended',
    plan: 'Pro',
    lastLogin: '1 month ago',
    created: 'Sep 15, 2022',
  },
  { 
    id: '8', 
    name: 'Leonard Krasner', 
    email: '<EMAIL>', 
    role: 'User', 
    status: 'active',
    plan: 'Basic',
    lastLogin: '12 hours ago',
    created: 'May 8, 2023',
  },
];

export default function Users() {
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage] = useState(5);
  
  // Filter users based on search term, role, and status
  const filteredUsers = usersData.filter(user => {
    const matchesSearch = 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = roleFilter === 'all' || user.role.toLowerCase() === roleFilter.toLowerCase();
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });
  
  // Pagination
  const indexOfLastUser = currentPage * usersPerPage;
  const indexOfFirstUser = indexOfLastUser - usersPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <span className="h-1.5 w-1.5 rounded-full bg-green-400 mr-1.5 animate-pulse"></span>
            Active
          </span>
        );
      case 'inactive':
        return (
          <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
            <span className="h-1.5 w-1.5 rounded-full bg-gray-400 mr-1.5"></span>
            Inactive
          </span>
        );
      case 'suspended':
        return (
          <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
            <span className="h-1.5 w-1.5 rounded-full bg-red-400 mr-1.5"></span>
            Suspended
          </span>
        );
      default:
        return null;
    }
  };
  
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'Admin':
        return (
          <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
            {role}
          </span>
        );
      case 'User':
        return (
          <span className="inline-flex items-center rounded-full bg-blue-900/30 border border-blue-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-blue-300">
            {role}
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
            {role}
          </span>
        );
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">User Management</h1>
          <p className="mt-1 text-sm text-gray-400">Manage user accounts and permissions</p>
        </div>
        <Button 
          variant="glass" 
          glow={true}
          icon={<PlusIcon className="h-5 w-5" />}
        >
          Add User
        </Button>
      </div>
      
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          <div className="relative flex-grow max-w-md">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10 
                placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              placeholder="Search users by name or email"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <select
              className="rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10 
                focus:ring-2 focus:ring-inset focus:ring-red-500 
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
            >
              <option value="all">All Roles</option>
              <option value="admin">Admin</option>
              <option value="user">User</option>
            </select>
            
            <select
              className="rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10 
                focus:ring-2 focus:ring-inset focus:ring-red-500 
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
            </select>
          </div>
        </div>
        
        <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
          <table className="min-w-full divide-y divide-white/10">
            <thead>
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                  Name
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Email
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Role
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Status
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Plan
                </th>
                <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {currentUsers.map((user) => (
                <tr key={user.id} className="hover:bg-white/5 transition-colors duration-200">
                  <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                    {user.name}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                    {user.email}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm">
                    {getRoleBadge(user.role)}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm">
                    {getStatusBadge(user.status)}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                    {user.plan}
                  </td>
                  <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <div className="flex justify-end space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="p-1"
                        title="Edit User"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="p-1"
                        title="Reset Password"
                      >
                        <LockClosedIcon className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="p-1"
                        title="Send Email"
                      >
                        <EnvelopeIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between border-t border-white/10 bg-white/5 px-4 py-3 sm:px-6 mt-4 rounded-xl">
            <div className="flex flex-1 justify-between sm:hidden">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-400">
                  Showing <span className="font-medium text-white">{indexOfFirstUser + 1}</span> to{' '}
                  <span className="font-medium text-white">
                    {Math.min(indexOfLastUser, filteredUsers.length)}
                  </span>{' '}
                  of <span className="font-medium text-white">{filteredUsers.length}</span> results
                </p>
              </div>
              <div>
                <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-l-md"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  {Array.from({ length: totalPages }).map((_, index) => (
                    <Button
                      key={index}
                      variant={currentPage === index + 1 ? 'glass' : 'outline'}
                      size="sm"
                      className="px-4"
                      onClick={() => setCurrentPage(index + 1)}
                    >
                      {index + 1}
                    </Button>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-r-md"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
