import { useState } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  GlobeAltIcon,
  KeyIcon,
  BellIcon,
  UserGroupIcon,
  CodeBracketIcon,
  ShieldCheckIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';

export default function Settings() {
  const [activeTab, setActiveTab] = useState('general');
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Account Settings</h1>
          <p className="mt-1 text-sm text-gray-400">Manage your account settings and preferences</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Card className="p-6 sticky top-24">
            <nav className="space-y-1">
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'general'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('general')}
              >
                <GlobeAltIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                General
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'security'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('security')}
              >
                <ShieldCheckIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Security
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'notifications'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('notifications')}
              >
                <BellIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Notifications
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'team'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('team')}
              >
                <UserGroupIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Team
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'api'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('api')}
              >
                <CodeBracketIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                API Keys
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'danger'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('danger')}
              >
                <TrashIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Danger Zone
              </button>
            </nav>
          </Card>
        </div>
        
        {/* Main content */}
        <div className="lg:col-span-3">
          {activeTab === 'general' && (
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">General Settings</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-md font-medium text-white mb-4">Account Information</h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="username" className="block text-sm font-medium text-gray-300">
                        Username
                      </label>
                      <input
                        type="text"
                        name="username"
                        id="username"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="johndoe"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                        Email Address
                      </label>
                      <input
                        type="email"
                        name="email"
                        id="email"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="<EMAIL>"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="pt-6 border-t border-white/10">
                  <h3 className="text-md font-medium text-white mb-4">Preferences</h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="language" className="block text-sm font-medium text-gray-300">
                        Language
                      </label>
                      <select
                        id="language"
                        name="language"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="en"
                      >
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="ja">Japanese</option>
                      </select>
                    </div>
                    <div>
                      <label htmlFor="timezone" className="block text-sm font-medium text-gray-300">
                        Timezone
                      </label>
                      <select
                        id="timezone"
                        name="timezone"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="America/Los_Angeles"
                      >
                        <option value="America/Los_Angeles">Pacific Time (US & Canada)</option>
                        <option value="America/New_York">Eastern Time (US & Canada)</option>
                        <option value="UTC">UTC</option>
                        <option value="Europe/London">London</option>
                        <option value="Asia/Tokyo">Tokyo</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="mt-4 space-y-4">
                    <div className="flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="dark-mode"
                          name="dark-mode"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-secondary-600 focus:ring-secondary-600"
                          defaultChecked
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="dark-mode" className="font-medium text-gray-300">
                          Dark Mode
                        </label>
                        <p className="text-gray-500">Use dark theme for the dashboard</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button variant="outline" className="mr-3">
                    Cancel
                  </Button>
                  <Button variant="glass" glow={true}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </Card>
          )}
          
          {activeTab === 'security' && (
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">Security Settings</h2>
              
              <div className="space-y-8">
                <div>
                  <h3 className="text-md font-medium text-white mb-4 flex items-center">
                    <KeyIcon className="h-5 w-5 mr-2 text-secondary-400" />
                    Change Password
                  </h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="current-password" className="block text-sm font-medium text-gray-300">
                        Current Password
                      </label>
                      <input
                        type="password"
                        name="current-password"
                        id="current-password"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                      />
                    </div>
                    <div className="sm:col-span-2 grid grid-cols-1 gap-6 sm:grid-cols-2">
                      <div>
                        <label htmlFor="new-password" className="block text-sm font-medium text-gray-300">
                          New Password
                        </label>
                        <input
                          type="password"
                          name="new-password"
                          id="new-password"
                          className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                            placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                            sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        />
                      </div>
                      <div>
                        <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-300">
                          Confirm New Password
                        </label>
                        <input
                          type="password"
                          name="confirm-password"
                          id="confirm-password"
                          className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                            placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                            sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex justify-end">
                    <Button variant="glass">
                      Update Password
                    </Button>
                  </div>
                </div>
                
                <div className="pt-6 border-t border-white/10">
                  <h3 className="text-md font-medium text-white mb-4">Two-Factor Authentication</h3>
                  <p className="text-sm text-gray-400 mb-4">
                    Add an extra layer of security to your account by enabling two-factor authentication.
                  </p>
                  <div className="p-4 rounded-xl bg-white/5 border border-white/10">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <ShieldCheckIcon className="h-8 w-8 text-secondary-400 mr-3" />
                        <div>
                          <h4 className="text-sm font-medium text-white">Two-Factor Authentication</h4>
                          <p className="text-xs text-gray-400 mt-1">Not enabled</p>
                        </div>
                      </div>
                      <Button variant="glass">
                        Enable
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="pt-6 border-t border-white/10">
                  <h3 className="text-md font-medium text-white mb-4">Sessions</h3>
                  <p className="text-sm text-gray-400 mb-4">
                    Manage your active sessions and sign out from other devices.
                  </p>
                  <div className="space-y-4">
                    <div className="p-4 rounded-xl bg-white/5 border border-white/10">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-white">Current Session</h4>
                          <p className="text-xs text-gray-400 mt-1">Windows 11 • Chrome • San Francisco, CA</p>
                        </div>
                        <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
                          Active Now
                        </span>
                      </div>
                    </div>
                    <div className="p-4 rounded-xl bg-white/5 border border-white/10">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-white">Mobile App</h4>
                          <p className="text-xs text-gray-400 mt-1">iOS 16 • iPhone • Last active 2 hours ago</p>
                        </div>
                        <Button variant="outline" size="sm">
                          Sign Out
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex justify-end">
                    <Button variant="outline">
                      Sign Out All Other Sessions
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          )}
          
          {activeTab === 'api' && (
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">API Keys</h2>
              
              <div className="space-y-6">
                <p className="text-sm text-gray-400">
                  API keys allow you to authenticate with our API and make requests to our endpoints.
                </p>
                
                <div className="p-4 rounded-xl bg-white/5 border border-white/10">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-white">Production API Key</h4>
                      <p className="text-xs text-gray-400 mt-1">Created on May 12, 2023</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        Reveal
                      </Button>
                      <Button variant="outline" size="sm">
                        Regenerate
                      </Button>
                    </div>
                  </div>
                  <div className="mt-4">
                    <input
                      type="password"
                      readOnly
                      className="block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm"
                      value="••••••••••••••••••••••••••••••"
                    />
                  </div>
                </div>
                
                <div className="p-4 rounded-xl bg-white/5 border border-white/10">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-white">Development API Key</h4>
                      <p className="text-xs text-gray-400 mt-1">Created on Jun 3, 2023</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        Reveal
                      </Button>
                      <Button variant="outline" size="sm">
                        Regenerate
                      </Button>
                    </div>
                  </div>
                  <div className="mt-4">
                    <input
                      type="password"
                      readOnly
                      className="block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm"
                      value="••••••••••••••••••••••••••••••"
                    />
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button variant="glass" glow={true}>
                    Create New API Key
                  </Button>
                </div>
              </div>
            </Card>
          )}
          
          {activeTab === 'danger' && (
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">Danger Zone</h2>
              
              <div className="space-y-6">
                <div className="p-6 rounded-xl bg-red-900/10 border border-red-500/20">
                  <h3 className="text-md font-medium text-white mb-2">Delete Account</h3>
                  <p className="text-sm text-gray-400 mb-4">
                    Once you delete your account, there is no going back. Please be certain.
                  </p>
                  <Button variant="outline" className="text-red-400 border-red-500/30 hover:bg-red-900/20">
                    Delete Account
                  </Button>
                </div>
                
                <div className="p-6 rounded-xl bg-yellow-900/10 border border-yellow-500/20">
                  <h3 className="text-md font-medium text-white mb-2">Export Data</h3>
                  <p className="text-sm text-gray-400 mb-4">
                    Download all your data including projects, domains, and billing history.
                  </p>
                  <Button variant="outline" className="text-yellow-400 border-yellow-500/30 hover:bg-yellow-900/20">
                    Export All Data
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
