import { useState, useEffect } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  UsersIcon, 
  ServerIcon, 
  CreditCardIcon, 
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  BarElement,
  Title, 
  Tooltip, 
  Legend, 
  Filler
);

// Sample data for stats
const stats = [
  { name: 'Total Users', value: '1,284', change: '12%', changeType: 'increase', icon: UsersIcon },
  { name: 'Active Servers', value: '342', change: '8%', changeType: 'increase', icon: ServerIcon },
  { name: 'Monthly Revenue', value: '$48,290', change: '5%', changeType: 'increase', icon: CreditCardIcon },
  { name: 'System Health', value: '99.8%', change: '0.2%', changeType: 'decrease', icon: ShieldCheckIcon },
];

// Sample data for alerts
const alerts = [
  {
    id: '1',
    title: 'High CPU Usage',
    description: 'Server cluster us-east-1 is experiencing high CPU usage (92%).',
    severity: 'high',
    time: '15 minutes ago',
  },
  {
    id: '2',
    title: 'Storage Space Low',
    description: 'Storage node eu-west-2-s3 is at 85% capacity.',
    severity: 'medium',
    time: '2 hours ago',
  },
  {
    id: '3',
    title: 'Payment Processing Error',
    description: 'Payment gateway integration is experiencing intermittent errors.',
    severity: 'medium',
    time: '5 hours ago',
  },
  {
    id: '4',
    title: 'New Security Update Available',
    description: 'Security patch v2.3.4 is available for deployment.',
    severity: 'low',
    time: '1 day ago',
  },
];

// Sample data for recent users
const recentUsers = [
  { id: '1', name: 'Jane Cooper', email: '<EMAIL>', plan: 'Pro', joined: '2 days ago' },
  { id: '2', name: 'Michael Foster', email: '<EMAIL>', plan: 'Basic', joined: '3 days ago' },
  { id: '3', name: 'Dries Vincent', email: '<EMAIL>', plan: 'Enterprise', joined: '1 week ago' },
  { id: '4', name: 'Lindsay Walton', email: '<EMAIL>', plan: 'Pro', joined: '2 weeks ago' },
];

export default function AdminDashboard() {
  const [animatedData, setAnimatedData] = useState<number[][]>([
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
  ]);
  
  // Real data for the charts
  const finalData = [
    [65, 59, 80, 81, 56, 55, 70], // Revenue data
    [28, 48, 40, 19, 86, 27, 90], // User signups data
  ];
  
  // Animate the chart data on component mount
  useEffect(() => {
    const animationDuration = 1500; // ms
    const steps = 30;
    const stepDuration = animationDuration / steps;
    
    let currentStep = 0;
    
    const interval = setInterval(() => {
      if (currentStep >= steps) {
        clearInterval(interval);
        setAnimatedData(finalData);
        return;
      }
      
      const progress = (currentStep + 1) / steps;
      const newData = finalData.map((dataset) => 
        dataset.map((value) => Math.round(value * progress))
      );
      
      setAnimatedData(newData);
      currentStep++;
    }, stepDuration);
    
    return () => clearInterval(interval);
  }, []);

  // Revenue chart data
  const revenueChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
    datasets: [
      {
        label: 'Revenue ($K)',
        data: animatedData[0],
        fill: true,
        backgroundColor: 'rgba(220, 38, 38, 0.2)',
        borderColor: 'rgba(220, 38, 38, 1)',
        borderWidth: 2,
        tension: 0.4,
        pointBackgroundColor: 'rgba(220, 38, 38, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(220, 38, 38, 1)',
        pointRadius: 4,
      },
    ],
  };

  // User signups chart data
  const userSignupsChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
    datasets: [
      {
        label: 'New Users',
        data: animatedData[1],
        backgroundColor: 'rgba(220, 38, 38, 0.7)',
        borderColor: 'rgba(220, 38, 38, 1)',
        borderWidth: 1,
        borderRadius: 4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        titleFont: {
          family: 'Poppins',
          size: 13,
        },
        bodyFont: {
          family: 'Poppins',
          size: 12,
        },
        padding: 12,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
    },
    animation: {
      duration: 1000,
      easing: 'easeOutQuart',
    },
  };

  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ');
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card 
            key={stat.name} 
            className="p-6 relative overflow-hidden"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 rounded-full bg-red-900/30 border border-red-500/30 flex items-center justify-center">
                  <stat.icon className="h-6 w-6 text-red-400" aria-hidden="true" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-300 truncate">{stat.name}</dt>
                  <dd>
                    <div className="text-lg font-semibold text-white">{stat.value}</div>
                  </dd>
                </dl>
              </div>
            </div>
            <div className="absolute bottom-0 inset-x-0 bg-gradient-to-r from-red-500/0 via-red-500/10 to-red-500/0 h-0.5"></div>
            
            <div className="absolute top-6 right-6">
              <div
                className={classNames(
                  stat.changeType === 'increase' ? 'bg-green-900/50 text-green-300 border border-green-500/30' : 'bg-red-900/50 text-red-300 border border-red-500/30',
                  'inline-flex items-baseline rounded-full px-2.5 py-0.5 text-sm font-medium backdrop-blur-sm'
                )}
              >
                {stat.changeType === 'increase' ? (
                  <ArrowUpIcon
                    className="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center text-green-400"
                    aria-hidden="true"
                  />
                ) : (
                  <ArrowDownIcon
                    className="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center text-red-400"
                    aria-hidden="true"
                  />
                )}
                {stat.change}
              </div>
            </div>
          </Card>
        ))}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Revenue Overview</h3>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">Monthly</Button>
                <Button variant="glass" size="sm">Export</Button>
              </div>
            </div>
            <div className="h-80 relative">
              <Line data={revenueChartData} options={chartOptions} />
            </div>
          </Card>
        </div>
        
        <div className="lg:col-span-1">
          <Card className="p-6 relative overflow-hidden h-full">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">System Alerts</h3>
              <Button variant="outline" size="sm">View All</Button>
            </div>
            
            <div className="space-y-4">
              {alerts.map((alert) => (
                <div 
                  key={alert.id} 
                  className={classNames(
                    alert.severity === 'high' ? 'border-red-500/30 bg-red-900/10' : 
                    alert.severity === 'medium' ? 'border-yellow-500/30 bg-yellow-900/10' : 
                    'border-blue-500/30 bg-blue-900/10',
                    'p-4 rounded-lg border'
                  )}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <ExclamationTriangleIcon 
                        className={classNames(
                          alert.severity === 'high' ? 'text-red-400' : 
                          alert.severity === 'medium' ? 'text-yellow-400' : 
                          'text-blue-400',
                          'h-5 w-5'
                        )} 
                        aria-hidden="true" 
                      />
                    </div>
                    <div className="ml-3 flex-1">
                      <h3 className="text-sm font-medium text-white">{alert.title}</h3>
                      <div className="mt-1 text-xs text-gray-400">
                        <p>{alert.description}</p>
                        <p className="mt-1 text-gray-500">{alert.time}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">New Users</h3>
              <Button variant="outline" size="sm">View All</Button>
            </div>
            <div className="h-80 relative">
              <Bar data={userSignupsChartData} options={chartOptions} />
            </div>
          </Card>
        </div>
        
        <div className="lg:col-span-2">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Recent User Signups</h3>
              <Button variant="glass" size="sm">View All Users</Button>
            </div>
            
            <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
              <table className="min-w-full divide-y divide-white/10">
                <thead>
                  <tr>
                    <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                      Name
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Email
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Plan
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Joined
                    </th>
                    <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-white/10">
                  {recentUsers.map((user) => (
                    <tr key={user.id}>
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                        {user.name}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                        {user.email}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
                          {user.plan}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                        {user.joined}
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <Button variant="outline" size="sm">View</Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
