import { useState, useRef, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { 
  ArrowPathIcon,
  ArrowDownTrayIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

export default function ProjectShell() {
  const project = useOutletContext<any>();
  const [command, setCommand] = useState('');
  const [history, setHistory] = useState<string[]>([
    'Welcome to the shell for ' + project.name,
    'Type "help" for a list of available commands.',
    '',
  ]);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const terminalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Auto-scroll to bottom when history changes
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [history]);
  
  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);
  
  const handleCommand = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!command.trim()) return;
    
    // Add command to history
    setHistory(prev => [...prev, `$ ${command}`]);
    
    // Process command
    const cmd = command.trim().toLowerCase();
    
    if (cmd === 'help') {
      setHistory(prev => [
        ...prev,
        'Available commands:',
        '  help - Show this help message',
        '  clear - Clear the terminal',
        '  ls - List files',
        '  pwd - Show current directory',
        '  env - Show environment variables',
        '  exit - Exit the shell',
        '',
      ]);
    } else if (cmd === 'clear') {
      setHistory(['Terminal cleared.', '']);
    } else if (cmd === 'ls') {
      setHistory(prev => [
        ...prev,
        'app.js',
        'package.json',
        'node_modules/',
        'public/',
        'src/',
        '',
      ]);
    } else if (cmd === 'pwd') {
      setHistory(prev => [
        ...prev,
        '/app',
        '',
      ]);
    } else if (cmd === 'env') {
      setHistory(prev => [
        ...prev,
        ...project.environment.map((env: any) => `${env.key}=${env.value}`),
        '',
      ]);
    } else if (cmd === 'exit') {
      setHistory(prev => [
        ...prev,
        'Exiting shell...',
        '',
      ]);
    } else {
      setHistory(prev => [
        ...prev,
        `Command not found: ${command}`,
        '',
      ]);
    }
    
    // Add to command history
    setCommandHistory(prev => [...prev, command]);
    
    // Reset command and history index
    setCommand('');
    setHistoryIndex(-1);
  };
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle up arrow for command history
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (commandHistory.length > 0 && historyIndex < commandHistory.length - 1) {
        const newIndex = historyIndex + 1;
        setHistoryIndex(newIndex);
        setCommand(commandHistory[commandHistory.length - 1 - newIndex]);
      }
    }
    
    // Handle down arrow for command history
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        setCommand(commandHistory[commandHistory.length - 1 - newIndex]);
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        setCommand('');
      }
    }
  };
  
  const clearTerminal = () => {
    setHistory(['Terminal cleared.', '']);
  };

  return (
    <Card className="p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h2 className="text-lg font-semibold text-white">Shell</h2>
          <p className="text-sm text-gray-400">Interactive shell for your application</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            icon={<ArrowDownTrayIcon className="h-4 w-4" />}
          >
            Download Log
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            icon={<XMarkIcon className="h-4 w-4" />}
            onClick={clearTerminal}
          >
            Clear
          </Button>
        </div>
      </div>
      
      <div className="relative">
        <div 
          ref={terminalRef}
          className="h-[500px] overflow-y-auto rounded-xl border border-white/10 bg-gray-900 text-white font-mono text-sm p-4"
        >
          {history.map((line, index) => (
            <div key={index} className="whitespace-pre-wrap">
              {line}
            </div>
          ))}
          <form onSubmit={handleCommand} className="flex items-center mt-1">
            <span className="text-green-400 mr-2">$</span>
            <input
              ref={inputRef}
              type="text"
              className="flex-grow bg-transparent border-none outline-none text-white font-mono text-sm"
              value={command}
              onChange={(e) => setCommand(e.target.value)}
              onKeyDown={handleKeyDown}
              autoFocus
            />
          </form>
        </div>
      </div>
      
      <div className="mt-4 text-xs text-gray-400">
        <p>Tip: Use the up and down arrow keys to navigate through command history.</p>
      </div>
    </Card>
  );
}
