import { useState, useEffect, Suspense } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { Bars3Icon } from '@heroicons/react/24/outline';
import Sidebar from './Sidebar';
import UserMenu from './UserMenu';
import NotificationsMenu from './NotificationsMenu';
import LoadingScreen from '../ui/LoadingScreen';

export default function DashboardLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [pageTitle, setPageTitle] = useState('Dashboard');
  const location = useLocation();

  // Update page title based on current route
  useEffect(() => {
    const path = location.pathname.split('/').pop() || 'dashboard';
    const formattedTitle = path.charAt(0).toUpperCase() + path.slice(1);
    setPageTitle(formattedTitle);
    document.title = `PoolotHost | ${formattedTitle}`;
  }, [location]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      <div className="lg:pl-72">
        <header className="sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-white/10 bg-gray-900/80 backdrop-blur-lg px-4 shadow-md sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-white lg:hidden hover:bg-white/10 rounded-md transition-colors duration-200"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>

          {/* Separator */}
          <div className="h-6 w-px bg-white/10 lg:hidden" aria-hidden="true" />

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-white">{pageTitle}</h1>
            </div>

            <div className="flex flex-1 items-center justify-end gap-x-4">
              <NotificationsMenu />
              <div className="hidden sm:block h-6 w-px bg-white/10" aria-hidden="true" />
              <UserMenu />
            </div>
          </div>
        </header>

        <main className="py-10 animate-fade-in">
          <div className="px-4 sm:px-6 lg:px-8">
            <Suspense fallback={
              <div className="flex items-center justify-center py-12">
                <div className="animate-pulse flex space-x-4 items-center">
                  <div className="h-12 w-12 rounded-full bg-secondary-500/20"></div>
                  <div className="space-y-2">
                    <div className="h-4 w-36 bg-secondary-500/20 rounded"></div>
                    <div className="h-4 w-24 bg-secondary-500/20 rounded"></div>
                  </div>
                </div>
              </div>
            }>
              <Outlet />
            </Suspense>
          </div>
        </main>

        {/* Decorative background elements */}
        <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-secondary-600/5 rounded-full blur-3xl"></div>
          <div className="absolute top-1/4 -left-40 w-80 h-80 bg-primary-600/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 left-1/3 w-80 h-80 bg-secondary-600/5 rounded-full blur-3xl"></div>
        </div>
      </div>
    </div>
  );
}
