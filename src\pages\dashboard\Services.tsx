import { useState } from 'react';
import { Link } from 'react-router-dom';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  MagnifyingGlassIcon,
  ServerIcon,
  CheckCircleIcon,
  PauseIcon,
  ClockIcon,
  XCircleIcon,
  GlobeAltIcon,
  RocketLaunchIcon,
  ComputerDesktopIcon,
  ArrowRightIcon,
} from '@heroicons/react/24/outline';

// Sample data for services
const servicesData = [
  {
    id: '1',
    name: 'EasybotRust',
    status: 'deployed',
    runtime: 'Rust',
    region: 'Oregon',
    deployed: '3d',
    url: 'https://easybotrust.onrender.com',
  },
  {
    id: '2',
    name: 'Frontend-App',
    status: 'deployed',
    runtime: 'Node.js',
    region: 'Frankfurt',
    deployed: '1d',
    url: 'https://frontend-app.onrender.com',
  },
  {
    id: '3',
    name: 'API-Service',
    status: 'deployed',
    runtime: 'Python',
    region: 'Singapore',
    deployed: '5d',
    url: 'https://api-service.onrender.com',
  },
  {
    id: '4',
    name: 'Database-Backup',
    status: 'suspended',
    runtime: 'PostgreSQL',
    region: 'Oregon',
    deployed: '10d',
    url: 'https://db-backup.onrender.com',
  },
  {
    id: '5',
    name: 'Analytics-Worker',
    status: 'suspended',
    runtime: 'Node.js',
    region: 'Frankfurt',
    deployed: '7d',
    url: 'https://analytics-worker.onrender.com',
  },
];

export default function Services() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Filter services based on search term and status
  const filteredServices = servicesData.filter(service => {
    const matchesSearch =
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.runtime.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.region.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' ||
                          (statusFilter === 'active' && service.status === 'deployed') ||
                          (statusFilter === 'suspended' && service.status === 'suspended');

    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'deployed':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <CheckCircleIcon className="mr-1 h-3 w-3 text-green-400" />
            Deployed
          </span>
        );
      case 'suspended':
        return (
          <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
            <PauseIcon className="h-3 w-3 mr-1 text-gray-400" />
            Suspended
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-900/30 border border-yellow-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-yellow-300">
            <ClockIcon className="h-3 w-3 mr-1 text-yellow-400" />
            Pending
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
            <XCircleIcon className="h-3 w-3 mr-1 text-red-400" />
            Failed
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Services</h1>
          <p className="mt-1 text-sm text-gray-400">Manage your deployed services and applications</p>
        </div>
        <Button
          variant="glass"
          glow={true}
          icon={<ServerIcon className="h-5 w-5" />}
        >
          New Service
        </Button>
      </div>

      {/* Hosting Services Section */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-xl font-semibold text-white">Hosting Services</h2>
            <p className="mt-1 text-sm text-gray-400">Manage your web hosting applications</p>
          </div>
          <Button
            variant="glass"
            size="sm"
            as={Link}
            to="/dashboard/hosting"
            icon={<ArrowRightIcon className="h-5 w-5" />}
          >
            View All Hosting
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="p-5 relative overflow-hidden border border-white/10 hover:border-secondary-500/50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg hover:shadow-secondary-500/10">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center">
                <ComputerDesktopIcon className="h-8 w-8 text-secondary-500 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-white">Web Hosting</h3>
                  <p className="text-sm text-gray-400">Deploy websites and web applications</p>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <Button
                variant="glass"
                size="sm"
                as={Link}
                to="/dashboard/hosting"
                fullWidth
              >
                Manage Hosting
              </Button>
            </div>
          </Card>

          <Card className="p-5 relative overflow-hidden border border-white/10 hover:border-secondary-500/50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg hover:shadow-secondary-500/10">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center">
                <ServerIcon className="h-8 w-8 text-secondary-500 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-white">Databases</h3>
                  <p className="text-sm text-gray-400">Manage database instances</p>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <Button
                variant="glass"
                size="sm"
                as={Link}
                to="/dashboard/hosting"
                fullWidth
              >
                Manage Databases
              </Button>
            </div>
          </Card>

          <Card className="p-5 relative overflow-hidden border border-white/10 hover:border-secondary-500/50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg hover:shadow-secondary-500/10">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center">
                <RocketLaunchIcon className="h-8 w-8 text-secondary-500 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-white">Deploy New</h3>
                  <p className="text-sm text-gray-400">Create a new hosting service</p>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <Button
                variant="glass"
                size="sm"
                as={Link}
                to="/dashboard/hosting"
                fullWidth
              >
                Deploy Now
              </Button>
            </div>
          </Card>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          <div className="relative flex-grow max-w-md">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10
                placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              placeholder="Search services"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex space-x-2">
            <Button
              variant={statusFilter === 'all' ? 'glass' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('all')}
            >
              All ({servicesData.length})
            </Button>
            <Button
              variant={statusFilter === 'active' ? 'glass' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('active')}
            >
              Active ({servicesData.filter(s => s.status === 'deployed').length})
            </Button>
            <Button
              variant={statusFilter === 'suspended' ? 'glass' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('suspended')}
            >
              Suspended ({servicesData.filter(s => s.status === 'suspended').length})
            </Button>
          </div>
        </div>

        <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
          <table className="min-w-full divide-y divide-white/10">
            <thead>
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                  Service Name
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Status
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Runtime
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Region
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Deployed
                </th>
                <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {filteredServices.map((service) => (
                <tr key={service.id} className="hover:bg-white/5 transition-colors duration-200">
                  <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                    <Link to={`/dashboard/services/${service.id}`} className="flex items-center hover:text-secondary-400 transition-colors duration-200">
                      <GlobeAltIcon className="h-5 w-5 text-gray-400 mr-2" />
                      {service.name}
                    </Link>
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm">
                    {getStatusBadge(service.status)}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                    {service.runtime}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                    {service.region}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                    {service.deployed}
                  </td>
                  <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        as={Link}
                        to={service.url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Open
                      </Button>
                      <Button
                        variant="glass"
                        size="sm"
                        as={Link}
                        to={`/dashboard/services/${service.id}`}
                      >
                        Manage
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
}
