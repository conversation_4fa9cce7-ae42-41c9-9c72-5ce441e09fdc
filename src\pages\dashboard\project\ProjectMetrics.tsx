import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { 
  ArrowPathIcon,
  ClockIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  GlobeAltIcon,
  UserGroupIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  MapPinIcon,
} from '@heroicons/react/24/outline';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

export default function ProjectMetrics() {
  const project = useOutletContext<any>();
  const [timeRange, setTimeRange] = useState('7d');
  const [autoRefresh, setAutoRefresh] = useState(false);
  
  // Sample data for charts
  const labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  
  const visitorChartData = {
    labels,
    datasets: [
      {
        label: 'Visitors',
        data: [1250, 1300, 1280, 1350, 1400, 1380, 1420],
        borderColor: 'rgba(139, 92, 246, 1)',
        backgroundColor: 'rgba(139, 92, 246, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };
  
  const pageViewsChartData = {
    labels,
    datasets: [
      {
        label: 'Page Views',
        data: [3200, 3350, 3280, 3450, 3600, 3500, 3650],
        borderColor: 'rgba(14, 165, 233, 1)',
        backgroundColor: 'rgba(14, 165, 233, 0.5)',
        borderWidth: 1,
      },
    ],
  };
  
  const deviceChartData = {
    labels: ['Desktop', 'Mobile', 'Tablet'],
    datasets: [
      {
        data: [55, 35, 10],
        backgroundColor: [
          'rgba(139, 92, 246, 0.7)',
          'rgba(14, 165, 233, 0.7)',
          'rgba(34, 197, 94, 0.7)',
        ],
        borderColor: [
          'rgba(139, 92, 246, 1)',
          'rgba(14, 165, 233, 1)',
          'rgba(34, 197, 94, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };
  
  const locationChartData = {
    labels: ['United States', 'United Kingdom', 'Germany', 'France', 'Canada', 'Other'],
    datasets: [
      {
        data: [40, 15, 10, 8, 7, 20],
        backgroundColor: [
          'rgba(139, 92, 246, 0.7)',
          'rgba(14, 165, 233, 0.7)',
          'rgba(34, 197, 94, 0.7)',
          'rgba(239, 68, 68, 0.7)',
          'rgba(245, 158, 11, 0.7)',
          'rgba(107, 114, 128, 0.7)',
        ],
        borderColor: [
          'rgba(139, 92, 246, 1)',
          'rgba(14, 165, 233, 1)',
          'rgba(34, 197, 94, 1)',
          'rgba(239, 68, 68, 1)',
          'rgba(245, 158, 11, 1)',
          'rgba(107, 114, 128, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };
  
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.7)',
          font: {
            family: 'Poppins, sans-serif',
          },
        },
      },
    },
    scales: {
      y: {
        ticks: {
          color: 'rgba(255, 255, 255, 0.7)',
          font: {
            family: 'Poppins, sans-serif',
          },
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      x: {
        ticks: {
          color: 'rgba(255, 255, 255, 0.7)',
          font: {
            family: 'Poppins, sans-serif',
          },
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
  };
  
  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.7)',
          font: {
            family: 'Poppins, sans-serif',
          },
        },
      },
    },
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-white">Analytics & Metrics</h2>
          <p className="text-sm text-gray-400">Track your website's performance and visitor statistics</p>
        </div>
        <div className="flex space-x-2">
          <select
            className="rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10 
              focus:ring-2 focus:ring-inset focus:ring-secondary-500 
              sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
          
          <Button 
            variant={autoRefresh ? "glass" : "outline"}
            size="sm"
            icon={<ArrowPathIcon className={`h-4 w-4 ${autoRefresh ? 'animate-spin' : ''}`} />}
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? 'Auto-refreshing' : 'Refresh'}
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-4 relative overflow-hidden">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Total Visitors</h3>
            <UserGroupIcon className="h-5 w-5 text-secondary-400" />
          </div>
          <div className="flex items-baseline">
            <span className="text-2xl font-bold text-white">9,380</span>
            <span className="ml-2 text-sm text-green-400 flex items-center">
              <ArrowTrendingUpIcon className="h-3 w-3 mr-1" />
              12.5%
            </span>
          </div>
          <p className="text-xs text-gray-400 mt-1">vs previous period</p>
        </Card>
        
        <Card className="p-4 relative overflow-hidden">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Page Views</h3>
            <GlobeAltIcon className="h-5 w-5 text-primary-400" />
          </div>
          <div className="flex items-baseline">
            <span className="text-2xl font-bold text-white">24,030</span>
            <span className="ml-2 text-sm text-green-400 flex items-center">
              <ArrowTrendingUpIcon className="h-3 w-3 mr-1" />
              8.3%
            </span>
          </div>
          <p className="text-xs text-gray-400 mt-1">vs previous period</p>
        </Card>
        
        <Card className="p-4 relative overflow-hidden">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Avg. Session</h3>
            <ClockIcon className="h-5 w-5 text-green-400" />
          </div>
          <div className="flex items-baseline">
            <span className="text-2xl font-bold text-white">3:42</span>
            <span className="ml-2 text-sm text-green-400 flex items-center">
              <ArrowTrendingUpIcon className="h-3 w-3 mr-1" />
              5.1%
            </span>
          </div>
          <p className="text-xs text-gray-400 mt-1">vs previous period</p>
        </Card>
        
        <Card className="p-4 relative overflow-hidden">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Bounce Rate</h3>
            <ChartBarIcon className="h-5 w-5 text-yellow-400" />
          </div>
          <div className="flex items-baseline">
            <span className="text-2xl font-bold text-white">42.8%</span>
            <span className="ml-2 text-sm text-red-400 flex items-center">
              <ArrowTrendingUpIcon className="h-3 w-3 mr-1" />
              2.4%
            </span>
          </div>
          <p className="text-xs text-gray-400 mt-1">vs previous period</p>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold leading-6 text-white">Visitors Overview</h3>
            <div className="flex items-center text-sm text-gray-400">
              <ClockIcon className="h-4 w-4 mr-1" />
              <span>Last updated: 5 minutes ago</span>
            </div>
          </div>
          <div className="h-80">
            <Line data={visitorChartData} options={chartOptions} />
          </div>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold leading-6 text-white">Page Views</h3>
            <Button 
              variant="outline" 
              size="sm"
            >
              View Pages
            </Button>
          </div>
          <div className="h-80">
            <Bar data={pageViewsChartData} options={chartOptions} />
          </div>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold leading-6 text-white">Device Breakdown</h3>
            <div className="flex space-x-2">
              <div className="flex items-center">
                <ComputerDesktopIcon className="h-4 w-4 text-secondary-400 mr-1" />
                <span className="text-sm text-white">55%</span>
              </div>
              <div className="flex items-center">
                <DevicePhoneMobileIcon className="h-4 w-4 text-primary-400 mr-1" />
                <span className="text-sm text-white">35%</span>
              </div>
            </div>
          </div>
          <div className="h-80">
            <Doughnut data={deviceChartData} options={doughnutOptions} />
          </div>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold leading-6 text-white">Visitor Locations</h3>
            <div className="flex items-center">
              <MapPinIcon className="h-4 w-4 text-secondary-400 mr-1" />
              <span className="text-sm text-white">40% US</span>
            </div>
          </div>
          <div className="h-80">
            <Doughnut data={locationChartData} options={doughnutOptions} />
          </div>
        </Card>
      </div>
    </div>
  );
}
