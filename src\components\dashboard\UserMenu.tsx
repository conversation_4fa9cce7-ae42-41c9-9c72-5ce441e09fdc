import { Fragment, useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, Transition } from '@headlessui/react';
import {
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function UserMenu() {
  const [isOpen, setIsOpen] = useState(false);
  
  // Mock user data - in a real app, this would come from auth context
  const user = {
    name: '<PERSON>',
    email: '<EMAIL>',
    imageUrl: null, // If null, we'll show initials
  };
  
  // Get user initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  return (
    <Menu as="div" className="relative">
      <Menu.Button 
        className="flex items-center gap-x-1 rounded-full bg-white/10 p-1 pl-2 pr-3 text-sm font-medium text-white hover:bg-white/20 transition-colors duration-200"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="sr-only">Open user menu</span>
        {user.imageUrl ? (
          <img
            className="h-8 w-8 rounded-full"
            src={user.imageUrl}
            alt={user.name}
          />
        ) : (
          <div className="h-8 w-8 rounded-full bg-secondary-600 flex items-center justify-center text-white text-sm font-medium">
            {getInitials(user.name)}
          </div>
        )}
        <span className="hidden lg:flex lg:items-center">
          <span className="ml-2 text-sm font-medium text-white" aria-hidden="true">
            {user.name}
          </span>
          <ChevronDownIcon 
            className={`ml-1 h-4 w-4 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
            aria-hidden="true" 
          />
        </span>
      </Menu.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-200"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-150"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
        afterEnter={() => setIsOpen(true)}
        afterLeave={() => setIsOpen(false)}
      >
        <Menu.Items className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-xl bg-gray-900/90 backdrop-blur-xl border border-white/10 py-1 shadow-lg ring-1 ring-white/5 focus:outline-none">
          <div className="px-4 py-3 border-b border-white/10">
            <p className="text-sm text-white">{user.name}</p>
            <p className="text-xs text-gray-400 truncate">{user.email}</p>
          </div>
          <Menu.Item>
            {({ active }) => (
              <Link
                to="/dashboard/profile"
                className={classNames(
                  active ? 'bg-white/10' : '',
                  'flex items-center px-4 py-2 text-sm text-gray-300 hover:text-white transition-colors duration-200'
                )}
              >
                <UserCircleIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                Your Profile
              </Link>
            )}
          </Menu.Item>
          <Menu.Item>
            {({ active }) => (
              <Link
                to="/dashboard/settings"
                className={classNames(
                  active ? 'bg-white/10' : '',
                  'flex items-center px-4 py-2 text-sm text-gray-300 hover:text-white transition-colors duration-200'
                )}
              >
                <Cog6ToothIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                Settings
              </Link>
            )}
          </Menu.Item>
          <Menu.Item>
            {({ active }) => (
              <Link
                to="/login"
                className={classNames(
                  active ? 'bg-white/10' : '',
                  'flex items-center px-4 py-2 text-sm text-gray-300 hover:text-white transition-colors duration-200'
                )}
              >
                <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                Sign out
              </Link>
            )}
          </Menu.Item>
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
