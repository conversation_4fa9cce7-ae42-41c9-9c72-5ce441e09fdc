import { Fragment, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Menu, Transition } from '@headlessui/react';
import useAuthStore from '../../stores/authStore';
import authService from '../../services/authService';
import {
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function UserMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();

  // Handle logout
  const handleLogout = async () => {
    try {
      await authService.logout();
      logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      logout();
      navigate('/login');
    }
  };
  
  // Get user initials from name
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName[0]}${lastName[0]}`.toUpperCase();
  };

  // If no user data, don't render the menu
  if (!user) {
    return null;
  }

  const fullName = `${user.first_name} ${user.last_name}`;

  return (
    <Menu as="div" className="relative">
      <Menu.Button 
        className="flex items-center gap-x-1 rounded-full bg-white/10 p-1 pl-2 pr-3 text-sm font-medium text-white hover:bg-white/20 transition-colors duration-200"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="sr-only">Open user menu</span>
        <div className="h-8 w-8 rounded-full bg-secondary-600 flex items-center justify-center text-white text-sm font-medium">
          {getInitials(user.first_name, user.last_name)}
        </div>
        <span className="hidden lg:flex lg:items-center">
          <span className="ml-2 text-sm font-medium text-white" aria-hidden="true">
            {fullName}
          </span>
          <ChevronDownIcon 
            className={`ml-1 h-4 w-4 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
            aria-hidden="true" 
          />
        </span>
      </Menu.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-200"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-150"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
        afterEnter={() => setIsOpen(true)}
        afterLeave={() => setIsOpen(false)}
      >
        <Menu.Items className="absolute right-0 z-10 mt-2 w-72 origin-top-right rounded-xl bg-gray-900/90 backdrop-blur-xl border border-white/10 py-1 shadow-lg ring-1 ring-white/5 focus:outline-none">
          <div className="px-4 py-4 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 rounded-full bg-secondary-600 flex items-center justify-center text-white text-lg font-medium">
                {getInitials(user.first_name, user.last_name)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">{fullName}</p>
                <p className="text-xs text-gray-400 truncate">{user.email}</p>
                <p className="text-xs text-gray-500 capitalize">{user.role}</p>
              </div>
            </div>
            <div className="mt-3 pt-3 border-t border-white/10">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-400">Recent Activity</span>
                <Link
                  to="/dashboard/profile"
                  className="text-xs text-secondary-400 hover:text-secondary-300 transition-colors"
                >
                  View All
                </Link>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Last login: {new Date(user.last_login).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
          </div>
          <Menu.Item>
            {({ active }) => (
              <Link
                to="/dashboard/profile"
                className={classNames(
                  active ? 'bg-white/10' : '',
                  'flex items-center px-4 py-2 text-sm text-gray-300 hover:text-white transition-colors duration-200'
                )}
              >
                <UserCircleIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                Your Profile
              </Link>
            )}
          </Menu.Item>
          <Menu.Item>
            {({ active }) => (
              <Link
                to="/dashboard/settings"
                className={classNames(
                  active ? 'bg-white/10' : '',
                  'flex items-center px-4 py-2 text-sm text-gray-300 hover:text-white transition-colors duration-200'
                )}
              >
                <Cog6ToothIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                Settings
              </Link>
            )}
          </Menu.Item>
          <Menu.Item>
            {({ active }) => (
              <button
                onClick={handleLogout}
                className={classNames(
                  active ? 'bg-white/10' : '',
                  'flex items-center w-full px-4 py-2 text-sm text-gray-300 hover:text-white transition-colors duration-200'
                )}
              >
                <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                Sign out
              </button>
            )}
          </Menu.Item>
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
