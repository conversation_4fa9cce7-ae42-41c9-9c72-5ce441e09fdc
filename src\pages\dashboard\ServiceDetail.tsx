import { useState, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useSimpleApiCall } from '../../hooks/useApiCall';
import DeveloperTools from '../../components/dev-tools/DeveloperTools';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  ServerIcon,
  GlobeAltIcon,
  ClockIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChevronDownIcon,
  CodeBracketIcon,
  CommandLineIcon,
  DocumentTextIcon,
  CogIcon,
  BellIcon,
  KeyIcon,
} from '@heroicons/react/24/outline';
import ServiceLogs from '../../components/dashboard/ServiceLogs';
import ServiceEvents from '../../components/dashboard/ServiceEvents';
import ServiceEnvironment from '../../components/dashboard/ServiceEnvironment';

// Sample data for services
const servicesData = [
  {
    id: '1',
    name: 'EasybotRust',
    status: 'deployed',
    runtime: 'Rust',
    region: 'Oregon',
    deployed: '3d',
    url: 'https://easybotrust.onrender.com',
    repository: 'Qlaosh80/EasybotRust',
    branch: 'main',
    lastDeployment: {
      id: '38025c0',
      status: 'success',
      timestamp: 'May 17, 2025 at 10:13 PM',
      duration: '2m 45s',
    },
    logs: [
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Using SVC API key: API key found' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Starting worker thread 0' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Starting worker thread 1' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Starting worker thread 2' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Starting worker thread 3' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Starting snipe worker' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Snipe worker polling interval: 60 seconds' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Loading all active snipes from database...' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Loaded 0 pending snipes for bsc' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Loaded 1 pending snipes for sol' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Loaded 0 pending snipes for eth' },
      { timestamp: 'May 17 10:39:59 PM', level: 'info', message: 'Loaded 0 pending snipes for base' },
    ],
    events: [
      { 
        id: '1', 
        type: 'deploy', 
        status: 'success', 
        message: 'Deploy live for 38025c0, port fix', 
        timestamp: 'May 17, 2025 at 10:13 PM' 
      },
      { 
        id: '2', 
        type: 'deploy', 
        status: 'started', 
        message: 'Deploy started for 38025c0, port fix', 
        details: 'Render periodically runs zero-downtime maintenance to ensure platform reliability.',
        timestamp: 'May 17, 2025 at 10:12 PM' 
      },
      { 
        id: '3', 
        type: 'deploy', 
        status: 'success', 
        message: 'Deploy live for 38025c0, port fix', 
        timestamp: 'May 14, 2025 at 12:56 AM' 
      },
      { 
        id: '4', 
        type: 'deploy', 
        status: 'started', 
        message: 'Deploy started for 38025c0, port fix', 
        details: 'New commit via Auto-Deploy',
        timestamp: 'May 14, 2025 at 12:37 AM' 
      },
      { 
        id: '5', 
        type: 'deploy', 
        status: 'failed', 
        message: 'Deploy failed for 644a07b, added vendored ssl', 
        timestamp: 'May 13, 2025 at 11:45 PM' 
      },
    ],
    environment: [
      { key: 'ADMIN_BOT_TOKEN', value: '••••••••••••' },
      { key: 'API_DEXSCREENER_BASE', value: '••••••••••••' },
      { key: 'API_DEXSCREENER_CHART_BASE', value: '••••••••••••' },
      { key: 'API_DEXSCREENER_CHART_BSC', value: '••••••••••••' },
      { key: 'API_DEXSCREENER_CHART_ETH', value: '••••••••••••' },
      { key: 'API_DEXSCREENER_CHART_SOL', value: '••••••••••••' },
      { key: 'API_DEXSCREENER_TOKENS', value: '••••••••••••' },
      { key: 'PORT', value: '••••••••••••' },
    ],
  },
  // More services would be here
];

export default function ServiceDetail() {
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState('dashboard');

  // Memoized API call function to prevent recreation on every render
  const fetchServiceData = useCallback(async () => {
    // Simulate API call to fetch service details
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        const foundService = servicesData.find(s => s.id === id);
        resolve(foundService || null);
      }, 500);
    });
  }, [id]);

  // Use the simple hook for data fetching with proper memoization
  const { data: service, loading, error, refetch } = useSimpleApiCall(
    fetchServiceData,
    [id] // Only re-fetch when id changes
  );
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-secondary-500"></div>
      </div>
    );
  }
  
  if (!service) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-white mb-2">Service Not Found</h2>
        <p className="text-gray-400 mb-6">The service you're looking for doesn't exist or you don't have access to it.</p>
        <Button 
          variant="glass" 
          as={Link}
          to="/dashboard/services"
        >
          Back to Services
        </Button>
      </div>
    );
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'deployed':
      case 'success':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <CheckCircleIcon className="mr-1 h-3 w-3 text-green-400" />
            {status === 'deployed' ? 'Deployed' : 'Success'}
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
            <XCircleIcon className="h-3 w-3 mr-1 text-red-400" />
            Failed
          </span>
        );
      case 'started':
        return (
          <span className="inline-flex items-center rounded-full bg-blue-900/30 border border-blue-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-blue-300">
            <ClockIcon className="h-3 w-3 mr-1 text-blue-400" />
            Started
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center">
          <div className="mr-4">
            <ServerIcon className="h-10 w-10 text-secondary-500" />
          </div>
          <div>
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-white">{service.name}</h1>
              <div className="ml-3">{getStatusBadge(service.status)}</div>
            </div>
            <div className="flex items-center mt-1 text-sm text-gray-400">
              <span className="flex items-center">
                <CodeBracketIcon className="h-4 w-4 mr-1" />
                {service.runtime}
              </span>
              <span className="mx-2">•</span>
              <a 
                href={`https://github.com/${service.repository}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center hover:text-secondary-400 transition-colors duration-200"
              >
                {service.repository}
                <span className="ml-1 text-xs border border-gray-600 rounded px-1">{service.branch}</span>
              </a>
            </div>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            as="a"
            href={service.url}
            target="_blank"
            rel="noopener noreferrer"
            icon={<GlobeAltIcon className="h-4 w-4" />}
          >
            Open
          </Button>
          <Button 
            variant="glass" 
            size="sm"
            icon={<ArrowPathIcon className="h-4 w-4" />}
          >
            Deploy
            <ChevronDownIcon className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>
      
      <div className="flex border-b border-white/10 mb-6">
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'dashboard'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('dashboard')}
        >
          Dashboard
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'events'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('events')}
        >
          Events
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'logs'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('logs')}
        >
          Logs
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'environment'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('environment')}
        >
          Environment
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'settings'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('settings')}
        >
          Settings
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'files'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('files')}
        >
          <div className="flex items-center space-x-1">
            <CodeBracketIcon className="h-4 w-4" />
            <span>File Manager</span>
          </div>
        </button>
      </div>
      
      {activeTab === 'dashboard' && (
        <div className="space-y-6">
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-white mb-4">Service Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">Status</h3>
                <div className="flex items-center">
                  {getStatusBadge(service.status)}
                  <span className="ml-2 text-white">{service.status === 'deployed' ? 'Live' : 'Offline'}</span>
                </div>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">Region</h3>
                <div className="text-white">{service.region}</div>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">Last Deployed</h3>
                <div className="text-white">{service.deployed} ago</div>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">URL</h3>
                <a 
                  href={service.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-secondary-400 hover:text-secondary-300 transition-colors duration-200 truncate block"
                >
                  {service.url}
                </a>
              </div>
            </div>
          </Card>
          
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-white mb-4">Latest Deployment</h2>
            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className="mr-3">
                    {getStatusBadge(service.lastDeployment.status)}
                  </div>
                  <div>
                    <h3 className="text-white font-medium">
                      {service.lastDeployment.id}
                    </h3>
                    <p className="text-sm text-gray-400">
                      {service.lastDeployment.timestamp}
                    </p>
                  </div>
                </div>
                <div className="text-sm text-gray-400">
                  Duration: {service.lastDeployment.duration}
                </div>
              </div>
              <div className="mt-4 flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={<DocumentTextIcon className="h-4 w-4" />}
                >
                  View Logs
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={<ArrowPathIcon className="h-4 w-4" />}
                >
                  Redeploy
                </Button>
              </div>
            </div>
          </Card>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-white">Recent Events</h2>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setActiveTab('events')}
                >
                  View All
                </Button>
              </div>
              <div className="space-y-4">
                {service.events.slice(0, 3).map((event: any) => (
                  <div 
                    key={event.id} 
                    className="p-3 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="mr-3">
                          {getStatusBadge(event.status)}
                        </div>
                        <div>
                          <p className="text-sm text-white">{event.message}</p>
                          <p className="text-xs text-gray-400">{event.timestamp}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
            
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-white">Quick Actions</h2>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Button 
                  variant="outline" 
                  fullWidth
                  className="justify-start"
                  icon={<CommandLineIcon className="h-5 w-5" />}
                >
                  Shell
                </Button>
                <Button 
                  variant="outline" 
                  fullWidth
                  className="justify-start"
                  icon={<CogIcon className="h-5 w-5" />}
                >
                  Settings
                </Button>
                <Button 
                  variant="outline" 
                  fullWidth
                  className="justify-start"
                  icon={<KeyIcon className="h-5 w-5" />}
                >
                  Environment
                </Button>
                <Button 
                  variant="outline" 
                  fullWidth
                  className="justify-start"
                  icon={<BellIcon className="h-5 w-5" />}
                >
                  Alerts
                </Button>
              </div>
            </Card>
          </div>
        </div>
      )}
      
      {activeTab === 'events' && (
        <ServiceEvents events={service.events} />
      )}
      
      {activeTab === 'logs' && (
        <ServiceLogs logs={service.logs} />
      )}
      
      {activeTab === 'environment' && (
        <ServiceEnvironment environment={service.environment} />
      )}
      
      {activeTab === 'settings' && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-white mb-6">Service Settings</h2>
          <p className="text-gray-400">Settings page content will go here.</p>
        </Card>
      )}

      {activeTab === 'files' && (
        <div className="h-[800px]">
          <DeveloperTools />
        </div>
      )}
    </div>
  );
}
