import { useEffect, useState } from 'react';

export default function LoadingScreen() {
  const [progress, setProgress] = useState(0);
  
  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prevProgress) => {
        if (prevProgress >= 100) {
          clearInterval(timer);
          return 100;
        }
        return prevProgress + 5;
      });
    }, 100);
    
    return () => {
      clearInterval(timer);
    };
  }, []);
  
  return (
    <div className="fixed inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 z-50">
      <div className="relative">
        {/* Logo animation */}
        <div className="animate-bounce mb-8 flex items-center justify-center">
          <div className="h-16 w-16 rounded-full bg-secondary-600/20 flex items-center justify-center">
            <div className="h-12 w-12 rounded-full bg-secondary-600/40 flex items-center justify-center">
              <div className="h-8 w-8 rounded-full bg-secondary-600 flex items-center justify-center text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        </div>
        
        {/* Loading text */}
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">Loading</h2>
          <p className="text-gray-400">Please wait while we prepare your dashboard</p>
        </div>
        
        {/* Progress bar */}
        <div className="w-64 h-2 bg-gray-700 rounded-full overflow-hidden">
          <div 
            className="h-full bg-gradient-to-r from-primary-600 to-secondary-600 transition-all duration-300 ease-out"
            style={{ width: `${progress}%` }}
          />
        </div>
        
        {/* Progress percentage */}
        <div className="text-center mt-2">
          <span className="text-sm text-gray-400">{progress}%</span>
        </div>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute -z-10 -top-20 -right-20 w-64 h-64 bg-secondary-600/10 rounded-full blur-3xl"></div>
      <div className="absolute -z-10 -bottom-20 -left-20 w-64 h-64 bg-primary-600/10 rounded-full blur-3xl"></div>
    </div>
  );
}
