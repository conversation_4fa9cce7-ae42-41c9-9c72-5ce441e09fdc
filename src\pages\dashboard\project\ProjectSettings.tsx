import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { 
  ArrowPathIcon,
  TrashIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

export default function ProjectSettings() {
  const project = useOutletContext<any>();
  const [projectName, setProjectName] = useState(project.name);
  const [framework, setFramework] = useState(project.framework);
  const [region, setRegion] = useState(project.region);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  
  const handleSaveSettings = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would make an API call to update the project
    alert('Project settings saved successfully!');
  };
  
  const handleDeleteProject = () => {
    // In a real app, this would make an API call to delete the project
    alert('Project deleted successfully!');
    // Redirect to projects list
    window.location.href = '/dashboard/hosting';
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-white">General Settings</h2>
            <p className="text-sm text-gray-400">Configure your project settings</p>
          </div>
        </div>
        
        <form onSubmit={handleSaveSettings}>
          <div className="space-y-6">
            <div>
              <label htmlFor="project-name" className="block text-sm font-medium text-gray-400 mb-1">
                Project Name
              </label>
              <input
                type="text"
                id="project-name"
                className="block w-full rounded-xl border-0 py-2 px-4 text-white ring-1 ring-inset ring-white/10 
                  placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
              />
            </div>
            
            <div>
              <label htmlFor="framework" className="block text-sm font-medium text-gray-400 mb-1">
                Framework
              </label>
              <select
                id="framework"
                className="block w-full rounded-xl border-0 py-2 px-4 text-white ring-1 ring-inset ring-white/10 
                  focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                value={framework}
                onChange={(e) => setFramework(e.target.value)}
              >
                <option value="Next.js">Next.js</option>
                <option value="React">React</option>
                <option value="Vue.js">Vue.js</option>
                <option value="Angular">Angular</option>
                <option value="Express.js">Express.js</option>
                <option value="Django">Django</option>
                <option value="Flask">Flask</option>
                <option value="Ruby on Rails">Ruby on Rails</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="region" className="block text-sm font-medium text-gray-400 mb-1">
                Region
              </label>
              <select
                id="region"
                className="block w-full rounded-xl border-0 py-2 px-4 text-white ring-1 ring-inset ring-white/10 
                  focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                value={region}
                onChange={(e) => setRegion(e.target.value)}
              >
                <option value="us-east-1">US East (N. Virginia)</option>
                <option value="us-west-1">US West (N. California)</option>
                <option value="us-west-2">US West (Oregon)</option>
                <option value="eu-west-1">EU (Ireland)</option>
                <option value="eu-central-1">EU (Frankfurt)</option>
                <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                <option value="ap-southeast-2">Asia Pacific (Sydney)</option>
                <option value="ap-northeast-1">Asia Pacific (Tokyo)</option>
              </select>
            </div>
            
            <div className="flex justify-end">
              <Button 
                variant="glass" 
                type="submit"
                icon={<ArrowPathIcon className="h-4 w-4" />}
              >
                Save Changes
              </Button>
            </div>
          </div>
        </form>
      </Card>
      
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-white">Repository Settings</h2>
            <p className="text-sm text-gray-400">Configure your Git repository settings</p>
          </div>
        </div>
        
        <div className="space-y-6">
          <div>
            <label htmlFor="repository" className="block text-sm font-medium text-gray-400 mb-1">
              Repository URL
            </label>
            <input
              type="text"
              id="repository"
              className="block w-full rounded-xl border-0 py-2 px-4 text-white ring-1 ring-inset ring-white/10 
                placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              defaultValue={project.repository}
              readOnly
            />
          </div>
          
          <div>
            <label htmlFor="branch" className="block text-sm font-medium text-gray-400 mb-1">
              Branch
            </label>
            <input
              type="text"
              id="branch"
              className="block w-full rounded-xl border-0 py-2 px-4 text-white ring-1 ring-inset ring-white/10 
                placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              defaultValue={project.branch}
            />
          </div>
          
          <div className="flex justify-end">
            <Button 
              variant="glass"
              icon={<ArrowPathIcon className="h-4 w-4" />}
            >
              Update Repository
            </Button>
          </div>
        </div>
      </Card>
      
      <Card className="p-6 border border-red-500/20">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-red-400">Danger Zone</h2>
            <p className="text-sm text-gray-400">Irreversible actions for your project</p>
          </div>
        </div>
        
        {!showDeleteConfirm ? (
          <div className="flex justify-between items-center p-4 rounded-lg bg-red-900/10 border border-red-500/20">
            <div>
              <h3 className="text-sm font-medium text-white">Delete this project</h3>
              <p className="text-xs text-gray-400 mt-1">
                Once you delete a project, there is no going back. Please be certain.
              </p>
            </div>
            <Button 
              variant="outline" 
              color="red"
              icon={<TrashIcon className="h-4 w-4" />}
              onClick={() => setShowDeleteConfirm(true)}
            >
              Delete Project
            </Button>
          </div>
        ) : (
          <div className="p-4 rounded-lg bg-red-900/10 border border-red-500/20">
            <div className="flex items-center mb-4">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-400 mr-2" />
              <h3 className="text-sm font-medium text-white">Are you absolutely sure?</h3>
            </div>
            <p className="text-xs text-gray-400 mb-4">
              This action cannot be undone. This will permanently delete the <strong>{project.name}</strong> project, 
              and remove all associated resources.
            </p>
            <div className="flex justify-end space-x-3">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowDeleteConfirm(false)}
              >
                Cancel
              </Button>
              <Button 
                variant="solid" 
                color="red"
                size="sm"
                icon={<TrashIcon className="h-4 w-4" />}
                onClick={handleDeleteProject}
              >
                Yes, delete this project
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
