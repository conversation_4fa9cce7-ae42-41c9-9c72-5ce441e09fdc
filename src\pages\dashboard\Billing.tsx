import { useState } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  CreditCardIcon,
  ReceiptRefundIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CheckIcon,
  PlusIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';

// Sample data for billing
const currentPlan = {
  name: 'Standard',
  price: '$9.99',
  interval: 'month',
  features: [
    '2GB RAM',
    '1 CPU',
    '10GB Storage',
    'Unlimited bandwidth',
    'Custom domains',
    'Priority support',
  ],
};

const plans = [
  {
    id: 'free',
    name: 'Free',
    price: '$0',
    interval: 'month',
    features: [
      '512MB RAM',
      '0.1 CPU',
      '1GB Storage',
      'Unlimited bandwidth',
      'Automatic SSL',
      'Community support',
    ],
    popular: false,
  },
  {
    id: 'starter',
    name: 'Starter',
    price: '$3.99',
    interval: 'month',
    features: [
      '512MB RAM',
      '0.5 CPU',
      '2GB Storage',
      'Unlimited bandwidth',
      'Automatic SSL',
      'Community support',
    ],
    popular: false,
  },
  {
    id: 'tiny',
    name: 'Tiny',
    price: '$5.99',
    interval: 'month',
    features: [
      '1GB RAM',
      '1 CPU',
      '5GB Storage',
      'Unlimited bandwidth',
      'Automatic SSL',
      'Email support',
    ],
    popular: false,
  },
  {
    id: 'standard',
    name: 'Standard',
    price: '$9.99',
    interval: 'month',
    features: [
      '2GB RAM',
      '1 CPU',
      '10GB Storage',
      'Unlimited bandwidth',
      'Automatic SSL',
      'Priority support',
      'Custom domains',
    ],
    popular: true,
  },
  {
    id: 'pro',
    name: 'Pro',
    price: '$19.99',
    interval: 'month',
    features: [
      '4GB RAM',
      '2 CPU',
      '20GB Storage',
      'Unlimited bandwidth',
      'Automatic SSL',
      'Priority support',
      'Custom domains',
      'Background workers',
    ],
    popular: false,
  },
  {
    id: 'pro-plus',
    name: 'Pro Plus',
    price: '$34.99',
    interval: 'month',
    features: [
      '8GB RAM',
      '4 CPU',
      '40GB Storage',
      'Unlimited bandwidth',
      'Automatic SSL',
      'Priority support',
      'Custom domains',
      'Background workers',
      'Advanced monitoring',
    ],
    popular: false,
  },
  {
    id: 'pro-max',
    name: 'Pro Max',
    price: '$64.99',
    interval: 'month',
    features: [
      '16GB RAM',
      '4 CPU',
      '80GB Storage',
      'Unlimited bandwidth',
      'Automatic SSL',
      'Priority support',
      'Custom domains',
      'Background workers',
      'Advanced monitoring',
      'Auto-scaling',
    ],
    popular: false,
  },
  {
    id: 'pro-ultra',
    name: 'Pro Ultra',
    price: '$109.99',
    interval: 'month',
    features: [
      '32GB RAM',
      '8 CPU',
      '160GB Storage',
      'Unlimited bandwidth',
      'Automatic SSL',
      'Priority support',
      'Custom domains',
      'Background workers',
      'Advanced monitoring',
      'Auto-scaling',
      'SLA guarantee',
    ],
    popular: false,
  },
  {
    id: 'custom',
    name: 'Custom',
    price: 'Contact',
    interval: 'custom',
    features: [
      '64-512GB RAM',
      '16-64 CPU',
      '500GB+ Storage',
      'Unlimited bandwidth',
      'Automatic SSL',
      'Priority support',
      'Custom domains',
      'Background workers',
      'Advanced monitoring',
      'Auto-scaling',
      'SLA guarantee',
      'Dedicated account manager',
      'Custom infrastructure',
    ],
    popular: false,
  },
];

// Sample data for payment methods
const paymentMethods = [
  {
    id: '1',
    type: 'visa',
    last4: '4242',
    expiry: '04/24',
    default: true,
  },
  {
    id: '2',
    type: 'mastercard',
    last4: '5555',
    expiry: '08/25',
    default: false,
  },
];

// Sample data for invoices
const invoices = [
  {
    id: 'INV-001',
    date: 'Jul 1, 2023',
    amount: '$9.99',
    status: 'paid',
  },
  {
    id: 'INV-002',
    date: 'Jun 1, 2023',
    amount: '$9.99',
    status: 'paid',
  },
  {
    id: 'INV-003',
    date: 'May 1, 2023',
    amount: '$9.99',
    status: 'paid',
  },
  {
    id: 'INV-004',
    date: 'Apr 1, 2023',
    amount: '$9.99',
    status: 'paid',
  },
  {
    id: 'INV-005',
    date: 'Mar 1, 2023',
    amount: '$9.99',
    status: 'paid',
  },
];

export default function Billing() {
  const [activeTab, setActiveTab] = useState('overview');
  const [billingInterval, setBillingInterval] = useState('month');

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <CheckIcon className="mr-1 h-3 w-3 text-green-400" />
            Paid
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-900/30 border border-yellow-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-yellow-300">
            <ClockIcon className="mr-1 h-3 w-3 text-yellow-400" />
            Pending
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Billing & Subscription</h1>
          <p className="mt-1 text-sm text-gray-400">Manage your subscription, payment methods, and billing history</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant={activeTab === 'overview' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </Button>
          <Button
            variant={activeTab === 'plans' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('plans')}
          >
            Plans
          </Button>
          <Button
            variant={activeTab === 'payment' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('payment')}
          >
            Payment
          </Button>
        </div>
      </div>

      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">Current Plan</h2>

              <div className="flex flex-col md:flex-row md:items-center justify-between p-6 rounded-xl bg-white/5 border border-white/10">
                <div>
                  <div className="flex items-center">
                    <h3 className="text-xl font-bold text-white">{currentPlan.name} Plan</h3>
                    <span className="ml-3 inline-flex items-center rounded-full bg-secondary-900/30 border border-secondary-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-secondary-300">
                      Current
                    </span>
                  </div>
                  <p className="mt-1 text-2xl font-semibold text-white">
                    {currentPlan.price}<span className="text-sm text-gray-400">/{currentPlan.interval}</span>
                  </p>
                  <ul className="mt-4 space-y-2">
                    {currentPlan.features.map((feature) => (
                      <li key={feature} className="flex items-center text-sm text-gray-300">
                        <CheckIcon className="mr-2 h-4 w-4 text-secondary-400" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="mt-6 md:mt-0 flex flex-col space-y-2">
                  <Button variant="glass">
                    Upgrade Plan
                  </Button>
                  <Button variant="outline">
                    Cancel Subscription
                  </Button>
                </div>
              </div>

              <div className="mt-8">
                <h3 className="text-md font-medium text-white mb-4">Billing History</h3>
                <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
                  <table className="min-w-full divide-y divide-white/10">
                    <thead>
                      <tr>
                        <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                          Invoice
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                          Date
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                          Amount
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                          Status
                        </th>
                        <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                          <span className="sr-only">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-white/10">
                      {invoices.map((invoice) => (
                        <tr key={invoice.id} className="hover:bg-white/5 transition-colors duration-200">
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                            {invoice.id}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                            {invoice.date}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                            {invoice.amount}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm">
                            {getStatusBadge(invoice.status)}
                          </td>
                          <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                            <Button variant="outline" size="sm">
                              Download
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </Card>
          </div>

          <div className="lg:col-span-1">
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">Payment Method</h2>

              <div className="space-y-4">
                {paymentMethods.map((method) => (
                  <div
                    key={method.id}
                    className={`p-4 rounded-xl ${method.default ? 'bg-white/10 border border-white/20' : 'bg-white/5 border border-white/10'}`}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="h-10 w-16 rounded bg-white/10 flex items-center justify-center mr-4">
                          {method.type === 'visa' ? (
                            <span className="text-blue-400 font-bold">VISA</span>
                          ) : (
                            <span className="text-red-400 font-bold">MC</span>
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-white">
                            •••• •••• •••• {method.last4}
                          </p>
                          <p className="text-xs text-gray-400">
                            Expires {method.expiry}
                          </p>
                        </div>
                      </div>
                      {method.default && (
                        <span className="inline-flex items-center rounded-full bg-secondary-900/30 border border-secondary-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-secondary-300">
                          Default
                        </span>
                      )}
                    </div>
                  </div>
                ))}

                <Button
                  variant="outline"
                  fullWidth
                  className="mt-4"
                  icon={<PlusIcon className="h-5 w-5" />}
                >
                  Add Payment Method
                </Button>
              </div>

              <div className="mt-8 pt-6 border-t border-white/10">
                <h3 className="text-md font-medium text-white mb-4">Billing Information</h3>

                <div className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-300">
                      Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      id="name"
                      className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10
                        placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                      defaultValue="John Doe"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                      Email
                    </label>
                    <input
                      type="email"
                      name="email"
                      id="email"
                      className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10
                        placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                      defaultValue="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label htmlFor="address" className="block text-sm font-medium text-gray-300">
                      Address
                    </label>
                    <input
                      type="text"
                      name="address"
                      id="address"
                      className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10
                        placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                      defaultValue="123 Main St, San Francisco, CA 94103"
                    />
                  </div>
                  <Button variant="glass" fullWidth>
                    Update Billing Info
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      )}

      {activeTab === 'plans' && (
        <Card className="p-6">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-white">Choose the Right Plan for Your Needs</h2>
            <p className="mt-2 text-gray-400">All plans include a 14-day free trial with no credit card required</p>

            <div className="flex items-center justify-center mt-6">
              <span className={`text-sm ${billingInterval === 'month' ? 'text-white' : 'text-gray-400'}`}>Monthly</span>
              <button
                type="button"
                className="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-white/10 mx-3 transition-colors duration-200 ease-in-out focus:outline-none"
                role="switch"
                aria-checked={billingInterval === 'year'}
                onClick={() => setBillingInterval(billingInterval === 'month' ? 'year' : 'month')}
              >
                <span
                  aria-hidden="true"
                  className={`${
                    billingInterval === 'year' ? 'translate-x-5' : 'translate-x-0'
                  } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-secondary-500 shadow ring-0 transition duration-200 ease-in-out`}
                />
              </button>
              <span className={`text-sm ${billingInterval === 'year' ? 'text-white' : 'text-gray-400'}`}>
                Yearly <span className="text-secondary-400">(Save 20%)</span>
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`glass-card p-6 rounded-xl relative overflow-hidden ${
                  plan.popular ? 'border-secondary-500 ring-1 ring-secondary-500' : 'border-white/10'
                }`}
              >
                {plan.popular && (
                  <div className="absolute top-0 right-0 -mt-1 -mr-1 h-20 w-20 overflow-hidden">
                    <div className="absolute -right-6 top-4 w-24 transform rotate-45 bg-secondary-500 text-center text-xs font-semibold text-white py-1">
                      Popular
                    </div>
                  </div>
                )}

                <h3 className="text-xl font-bold text-white">{plan.name}</h3>
                <p className="mt-4 text-3xl font-bold text-white">
                  {billingInterval === 'month' ? plan.price : `$${parseInt(plan.price.replace('$', '')) * 10}`}
                  <span className="text-sm text-gray-400">/{billingInterval}</span>
                </p>

                <ul className="mt-6 space-y-4">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-start">
                      <CheckIcon className="h-5 w-5 text-secondary-400 flex-shrink-0 mt-0.5" />
                      <span className="ml-3 text-sm text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                <div className="mt-8">
                  <Button
                    variant={plan.popular ? 'glass' : 'outline'}
                    fullWidth
                    glow={plan.popular}
                  >
                    {currentPlan.name === plan.name ? 'Current Plan' : 'Choose Plan'}
                  </Button>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <h3 className="text-lg font-semibold text-white mb-2">Need more resources?</h3>
            <p className="text-gray-400 mb-4">
              For custom requirements or resources beyond Pro Ultra (64-512GB RAM, 16-64 CPU, 500GB+ storage),
              we offer tailored solutions for enterprise needs.
            </p>
            <p className="text-gray-400 mb-6">
              Contact our support team for a custom plan that meets your specific requirements.
            </p>
            <Button
              variant="glass"
              glow
              icon={<ChatBubbleLeftRightIcon className="h-5 w-5" />}
            >
              Contact Support
            </Button>
          </div>
        </Card>
      )}

      {activeTab === 'payment' && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-white mb-6">Payment Methods</h2>

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className={`p-6 rounded-xl ${method.default ? 'bg-white/10 border border-white/20' : 'bg-white/5 border border-white/10'}`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex items-center">
                      <div className="h-12 w-20 rounded bg-white/10 flex items-center justify-center mr-4">
                        {method.type === 'visa' ? (
                          <span className="text-blue-400 font-bold text-lg">VISA</span>
                        ) : (
                          <span className="text-red-400 font-bold text-lg">MC</span>
                        )}
                      </div>
                      <div>
                        <p className="text-md font-medium text-white">
                          •••• •••• •••• {method.last4}
                        </p>
                        <p className="text-sm text-gray-400 mt-1">
                          Expires {method.expiry}
                        </p>
                      </div>
                    </div>
                    {method.default ? (
                      <span className="inline-flex items-center rounded-full bg-secondary-900/30 border border-secondary-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-secondary-300">
                        Default
                      </span>
                    ) : (
                      <Button variant="outline" size="sm">
                        Make Default
                      </Button>
                    )}
                  </div>
                  <div className="mt-4 flex space-x-2">
                    <Button variant="outline" size="sm">
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <div className="p-6 rounded-xl border border-dashed border-white/20 flex flex-col items-center justify-center text-center">
              <CreditCardIcon className="h-12 w-12 text-gray-400 mb-3" />
              <h3 className="text-lg font-medium text-white mb-1">Add a new payment method</h3>
              <p className="text-sm text-gray-400 mb-4">Add a credit card or debit card</p>
              <Button
                variant="glass"
                icon={<PlusIcon className="h-5 w-5" />}
              >
                Add Payment Method
              </Button>
            </div>
          </div>

          <div className="mt-12">
            <h2 className="text-lg font-semibold text-white mb-6">Billing History</h2>

            <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
              <table className="min-w-full divide-y divide-white/10">
                <thead>
                  <tr>
                    <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                      Invoice
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Date
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Amount
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Status
                    </th>
                    <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-white/10">
                  {invoices.map((invoice) => (
                    <tr key={invoice.id} className="hover:bg-white/5 transition-colors duration-200">
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                        {invoice.id}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                        {invoice.date}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                        {invoice.amount}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        {getStatusBadge(invoice.status)}
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <Button
                          variant="outline"
                          size="sm"
                          icon={<ReceiptRefundIcon className="h-4 w-4" />}
                        >
                          Download
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}
