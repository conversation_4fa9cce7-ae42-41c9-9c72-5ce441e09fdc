import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  company: string | null;
  role: string;
  status: string;
  created_at: string;
  last_login: string;
}

export interface AuthResponse {
  success: boolean;
  data: {
    token: string;
    user: User;
    expires_at: string;
  };
  meta: {
    timestamp: string;
    request_id: string;
    trace_id: string;
    version: string;
    status_code: number;
  };
}

interface AuthState {
  // State
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  expiresAt: string | null;

  // Actions
  setAuth: (authData: AuthResponse['data']) => void;
  setUser: (user: User) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  logout: () => void;
  clearError: () => void;
  
  // Utility functions
  isTokenExpired: () => boolean;
  getAuthHeader: () => string | null;
}

const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      expiresAt: null,

      // Actions
      setAuth: (authData) => {
        set({
          user: authData.user,
          token: authData.token,
          expiresAt: authData.expires_at,
          isAuthenticated: true,
          error: null,
        });
      },

      setUser: (user) => {
        set({ user });
      },

      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      setError: (error) => {
        set({ error });
      },

      logout: () => {
        set({
          user: null,
          token: null,
          expiresAt: null,
          isAuthenticated: false,
          error: null,
        });
        // Clear token from localStorage
        localStorage.removeItem('auth-storage');
      },

      clearError: () => {
        set({ error: null });
      },

      // Utility functions
      isTokenExpired: () => {
        const { expiresAt } = get();
        if (!expiresAt) return true;
        
        const expirationTime = new Date(expiresAt).getTime();
        const currentTime = new Date().getTime();
        
        return currentTime >= expirationTime;
      },

      getAuthHeader: () => {
        const { token, isTokenExpired } = get();
        
        if (!token || isTokenExpired()) {
          return null;
        }
        
        return `Bearer ${token}`;
      },
    }),
    {
      name: 'auth-storage', // localStorage key
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        expiresAt: state.expiresAt,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

export default useAuthStore;
