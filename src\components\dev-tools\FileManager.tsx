import React, { useState, useRef, useCallback } from 'react';
import { 
  FolderIcon, 
  DocumentIcon, 
  CloudArrowUpIcon,
  PlusIcon,
  TrashIcon,
  PencilIcon,
  FolderPlusIcon,
  DocumentPlusIcon,
  ChevronRightIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';

interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  size?: number;
  lastModified?: Date;
  children?: FileNode[];
  content?: string;
  language?: string;
  isExpanded?: boolean;
}

interface FileManagerProps {
  onFileSelect: (file: FileNode) => void;
  selectedFile?: FileNode | null;
}

const FileManager: React.FC<FileManagerProps> = ({ onFileSelect, selectedFile }) => {
  const [fileTree, setFileTree] = useState<FileNode[]>([
    {
      id: '1',
      name: 'public',
      type: 'folder',
      isExpanded: true,
      children: [
        { id: '2', name: 'index.html', type: 'file', size: 1024, language: 'html' },
        { id: '3', name: 'favicon.ico', type: 'file', size: 512 }
      ]
    },
    {
      id: '4',
      name: 'src',
      type: 'folder',
      isExpanded: true,
      children: [
        { id: '5', name: 'App.js', type: 'file', size: 2048, language: 'javascript' },
        { id: '6', name: 'index.js', type: 'file', size: 512, language: 'javascript' },
        {
          id: '7',
          name: 'components',
          type: 'folder',
          children: [
            { id: '8', name: 'Header.js', type: 'file', size: 1536, language: 'javascript' }
          ]
        }
      ]
    },
    { id: '9', name: 'package.json', type: 'file', size: 1024, language: 'json' },
    { id: '10', name: 'README.md', type: 'file', size: 2048, language: 'markdown' }
  ]);

  const [dragOver, setDragOver] = useState(false);
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number; node: FileNode } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getFileIcon = (file: FileNode) => {
    if (file.type === 'folder') {
      return <FolderIcon className="h-4 w-4 text-blue-500" />;
    }
    
    const extension = file.name.split('.').pop()?.toLowerCase();
    const iconClass = "h-4 w-4";
    
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <DocumentIcon className={`${iconClass} text-yellow-500`} />;
      case 'html':
      case 'htm':
        return <DocumentIcon className={`${iconClass} text-orange-500`} />;
      case 'css':
      case 'scss':
      case 'sass':
        return <DocumentIcon className={`${iconClass} text-blue-500`} />;
      case 'json':
        return <DocumentIcon className={`${iconClass} text-green-500`} />;
      case 'md':
        return <DocumentIcon className={`${iconClass} text-gray-500`} />;
      default:
        return <DocumentIcon className={`${iconClass} text-gray-400`} />;
    }
  };

  const toggleFolder = useCallback((nodeId: string) => {
    const updateNode = (nodes: FileNode[]): FileNode[] => {
      return nodes.map(node => {
        if (node.id === nodeId && node.type === 'folder') {
          return { ...node, isExpanded: !node.isExpanded };
        }
        if (node.children) {
          return { ...node, children: updateNode(node.children) };
        }
        return node;
      });
    };
    
    setFileTree(updateNode(fileTree));
  }, [fileTree]);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach(file => {
      const newFile: FileNode = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: file.name,
        type: 'file',
        size: file.size,
        lastModified: new Date(file.lastModified),
        language: getLanguageFromExtension(file.name)
      };

      setFileTree(prev => [...prev, newFile]);
    });

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const getLanguageFromExtension = (filename: string): string => {
    const extension = filename.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'json': 'json',
      'md': 'markdown',
      'py': 'python',
      'php': 'php',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'go': 'go',
      'rs': 'rust',
      'rb': 'ruby',
      'yml': 'yaml',
      'yaml': 'yaml',
      'xml': 'xml',
      'sql': 'sql'
    };
    return languageMap[extension || ''] || 'plaintext';
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    Array.from(files).forEach(file => {
      const newFile: FileNode = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: file.name,
        type: 'file',
        size: file.size,
        lastModified: new Date(file.lastModified),
        language: getLanguageFromExtension(file.name)
      };

      setFileTree(prev => [...prev, newFile]);
    });
  }, []);

  const renderFileTree = (nodes: FileNode[], depth = 0) => {
    return nodes.map(node => (
      <div key={node.id} className="select-none">
        <div
          className={`flex items-center py-1 px-2 hover:bg-gray-700 cursor-pointer text-sm ${
            selectedFile?.id === node.id ? 'bg-gray-700' : ''
          }`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => {
            if (node.type === 'folder') {
              toggleFolder(node.id);
            } else {
              onFileSelect(node);
            }
          }}
          onContextMenu={(e) => {
            e.preventDefault();
            setContextMenu({ x: e.clientX, y: e.clientY, node });
          }}
        >
          {node.type === 'folder' && (
            <span className="mr-1">
              {node.isExpanded ? (
                <ChevronDownIcon className="h-3 w-3" />
              ) : (
                <ChevronRightIcon className="h-3 w-3" />
              )}
            </span>
          )}
          <span className="mr-2">{getFileIcon(node)}</span>
          <span className="flex-1 truncate text-gray-300">{node.name}</span>
          {node.type === 'file' && node.size && (
            <span className="text-xs text-gray-500 ml-2">
              {(node.size / 1024).toFixed(1)}KB
            </span>
          )}
        </div>
        {node.type === 'folder' && node.isExpanded && node.children && (
          <div>{renderFileTree(node.children, depth + 1)}</div>
        )}
      </div>
    ));
  };

  return (
    <div className="h-full flex flex-col bg-gray-800">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <h3 className="text-xs font-medium text-gray-300 uppercase tracking-wide">Files</h3>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => fileInputRef.current?.click()}
            className="p-1 text-gray-400 hover:text-gray-200 rounded"
            title="Upload Files"
          >
            <CloudArrowUpIcon className="h-4 w-4" />
          </button>
          <button
            className="p-1 text-gray-400 hover:text-gray-200 rounded"
            title="New File"
          >
            <DocumentPlusIcon className="h-4 w-4" />
          </button>
          <button
            className="p-1 text-gray-400 hover:text-gray-200 rounded"
            title="New Folder"
          >
            <FolderPlusIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* File Tree */}
      <div
        className={`flex-1 overflow-auto ${
          dragOver ? 'bg-blue-900/20 border-2 border-dashed border-blue-400' : ''
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {dragOver && (
          <div className="flex items-center justify-center h-32 text-blue-400">
            <div className="text-center">
              <CloudArrowUpIcon className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm">Drop files here to upload</p>
            </div>
          </div>
        )}
        {!dragOver && renderFileTree(fileTree)}
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        className="hidden"
        onChange={handleFileUpload}
      />


    </div>
  );
};

export default FileManager;
