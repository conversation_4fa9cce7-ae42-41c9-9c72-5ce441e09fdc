import { useState, useCallback, Suspense, Component, type ReactNode } from 'react';
import { useParams, Link, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useSimpleApiCall } from '../../hooks/useApiCall';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  HomeIcon,
  BellIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  DocumentTextIcon,
  ServerIcon,
  CommandLineIcon,
  KeyIcon,
  ArrowPathIcon,
  ScaleIcon,
  DocumentDuplicateIcon,
  CircleStackIcon,
  ClockIcon,
  CodeBracketIcon,
  GlobeAltIcon,
  ArrowTopRightOnSquareIcon,
  PauseIcon,
  PlayIcon,
  LinkIcon,
  WrenchScrewdriverIcon,
  ChartBarSquareIcon,
  ArrowsPointingOutIcon,
} from '@heroicons/react/24/outline';

// Sample data for projects
const projectsData = [
  {
    id: '1',
    name: 'E-commerce Website',
    status: 'running',
    environment: 'production',
    url: 'https://myshop.com',
    lastDeployed: '2 hours ago',
    cpu: '45%',
    memory: '512 MB',
    storage: '2.3 GB',
    framework: 'Next.js',
    region: 'us-east-1',
    repository: 'github.com/user/e-commerce',
    branch: 'main',
    deployments: [
      {
        id: 'dep-1',
        status: 'success',
        commit: 'a1b2c3d',
        message: 'Update product page',
        author: 'John Doe',
        timestamp: '2 hours ago',
        duration: '1m 45s',
      },
      {
        id: 'dep-2',
        status: 'success',
        commit: 'e4f5g6h',
        message: 'Fix checkout bug',
        author: 'Jane Smith',
        timestamp: '1 day ago',
        duration: '2m 12s',
      },
      {
        id: 'dep-3',
        status: 'failed',
        commit: 'i7j8k9l',
        message: 'Add payment gateway',
        author: 'John Doe',
        timestamp: '2 days ago',
        duration: '1m 30s',
      },
    ],
    events: [
      {
        id: 'evt-1',
        type: 'deployment',
        status: 'success',
        message: 'Deployment successful',
        timestamp: '2 hours ago',
      },
      {
        id: 'evt-2',
        type: 'restart',
        status: 'success',
        message: 'Service restarted',
        timestamp: '1 day ago',
      },
      {
        id: 'evt-3',
        type: 'deployment',
        status: 'failed',
        message: 'Deployment failed',
        timestamp: '2 days ago',
      },
    ],
    logs: [
      { timestamp: '2023-05-17 10:39:59', level: 'info', message: 'Server started on port 3000' },
      { timestamp: '2023-05-17 10:39:58', level: 'info', message: 'Connected to database' },
      { timestamp: '2023-05-17 10:39:57', level: 'info', message: 'Loading environment variables' },
      { timestamp: '2023-05-17 10:39:56', level: 'info', message: 'Initializing application' },
    ],
    metrics: {
      cpu: [45, 42, 47, 50, 48, 46, 44],
      memory: [512, 520, 510, 530, 525, 515, 518],
      requests: [1250, 1300, 1280, 1350, 1400, 1380, 1420],
    },
    environmentVars: [
      { key: 'DATABASE_URL', value: '••••••••••••' },
      { key: 'API_KEY', value: '••••••••••••' },
      { key: 'NODE_ENV', value: 'production' },
      { key: 'PORT', value: '3000' },
    ],
    connections: {
      ftp: {
        host: 'ftp.poolothost.com',
        port: '21',
        username: 'e-commerce-website',
        password: 'securePassword123'
      },
      ssh: {
        host: 'ssh.poolothost.com',
        port: '22',
        username: 'e-commerce-website',
        password: 'securePassword123'
      }
    },
  },
  // More projects would be here
];

// Error Boundary component to catch errors in child components
class ErrorBoundary extends Component<{ children: ReactNode }, { hasError: boolean, error: Error | null }> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Something went wrong</h3>
          <p className="text-gray-400 mb-4">There was an error loading this component.</p>
          <pre className="bg-gray-800 p-4 rounded-lg text-sm text-red-400 overflow-auto max-h-40 mb-4">
            {this.state.error?.toString()}
          </pre>
          <Button
            variant="glass"
            onClick={() => this.setState({ hasError: false, error: null })}
          >
            Try Again
          </Button>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Navigation items for project subpages
const projectNavigation = [
  { name: 'Dashboard', href: '', icon: HomeIcon },
  { name: 'Events', href: 'events', icon: BellIcon },
  { name: 'Settings', href: 'settings', icon: Cog6ToothIcon },
  { name: 'Monitor', href: 'monitor', icon: ChartBarIcon },
  { name: 'Logs', href: 'logs', icon: DocumentTextIcon },
  { name: 'Metrics', href: 'metrics', icon: ChartBarSquareIcon },
  { name: 'Manage', href: 'manage', icon: WrenchScrewdriverIcon },
  { name: 'Environment', href: 'environment', icon: KeyIcon },
  { name: 'Shell', href: 'shell', icon: CommandLineIcon },
  { name: 'Scaling', href: 'scaling', icon: ArrowsPointingOutIcon },
  { name: 'Previews', href: 'previews', icon: DocumentDuplicateIcon },
  { name: 'Disks', href: 'disks', icon: CircleStackIcon },
  { name: 'Jobs', href: 'jobs', icon: ClockIcon },
  { name: 'Connections', href: 'connections', icon: LinkIcon },
];

export default function ProjectDetail() {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const navigate = useNavigate();

  // Get the current active subpage
  const getActiveSubpage = () => {
    const path = location.pathname.split('/');
    return path[path.length - 1] === id ? '' : path[path.length - 1];
  };

  const activeSubpage = getActiveSubpage();

  // Memoized API call function to prevent recreation on every render
  const fetchProjectData = useCallback(async () => {
    // Simulate API call to fetch project details
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        const foundProject = projectsData.find(p => p.id === id);
        resolve(foundProject || null);
      }, 500);
    });
  }, [id]);

  // Use the simple hook for data fetching with proper memoization
  const { data: project, loading, error, refetch } = useSimpleApiCall(
    fetchProjectData,
    [id] // Only re-fetch when id changes
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-secondary-500"></div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-white mb-2">Project Not Found</h2>
        <p className="text-gray-400 mb-6">The project you're looking for doesn't exist or you don't have access to it.</p>
        <Button
          variant="glass"
          as={Link}
          to="/dashboard/hosting"
        >
          Back to Projects
        </Button>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <span className="h-1.5 w-1.5 rounded-full bg-green-400 mr-1.5 animate-pulse"></span>
            Running
          </span>
        );
      case 'stopped':
        return (
          <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
            <PauseIcon className="h-3 w-3 mr-1 text-gray-400" />
            Stopped
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center">
          <div className="mr-4">
            <ServerIcon className="h-10 w-10 text-secondary-500" />
          </div>
          <div>
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-white">{project.name}</h1>
              <div className="ml-3">{getStatusBadge(project.status)}</div>
            </div>
            <div className="flex items-center mt-1 text-sm text-gray-400">
              <span className="flex items-center">
                <CodeBracketIcon className="h-4 w-4 mr-1" />
                {project.framework}
              </span>
              <span className="mx-2">•</span>
              <span className="flex items-center">
                <GlobeAltIcon className="h-4 w-4 mr-1" />
                {project.region}
              </span>
            </div>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            as="a"
            href={project.url}
            target="_blank"
            rel="noopener noreferrer"
            icon={<ArrowTopRightOnSquareIcon className="h-4 w-4" />}
          >
            Open Site
          </Button>
          <Button
            variant="glass"
            size="sm"
            icon={project.status === 'running' ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
          >
            {project.status === 'running' ? 'Stop' : 'Start'}
          </Button>
          <Button
            variant="glass"
            size="sm"
            icon={<ArrowPathIcon className="h-4 w-4" />}
          >
            Redeploy
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-6 gap-8">
        {/* Sidebar navigation */}
        <div className="lg:col-span-1">
          <Card className="p-4 sticky top-24">
            <nav className="space-y-1">
              {projectNavigation.map((item) => {
                const isActive = activeSubpage === item.href;
                const href = `/dashboard/projects/${id}${item.href ? `/${item.href}` : ''}`;

                return (
                  <Link
                    key={item.name}
                    to={href}
                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      isActive
                        ? 'bg-white/10 text-white'
                        : 'text-gray-400 hover:text-white hover:bg-white/5'
                    }`}
                  >
                    <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                    {item.name}
                  </Link>
                );
              })}
            </nav>
          </Card>
        </div>

        {/* Main content area */}
        <div className="lg:col-span-5">
          <Suspense fallback={
            <div className="flex items-center justify-center py-12">
              <div className="animate-pulse flex space-x-4 items-center">
                <div className="h-12 w-12 rounded-full bg-secondary-500/20"></div>
                <div className="space-y-2">
                  <div className="h-4 w-36 bg-secondary-500/20 rounded"></div>
                  <div className="h-4 w-24 bg-secondary-500/20 rounded"></div>
                </div>
              </div>
            </div>
          }>
            {/* Add error boundary to catch errors in child components */}
            <ErrorBoundary>
              <Outlet context={project} />
            </ErrorBoundary>
          </Suspense>
        </div>
      </div>
    </div>
  );
}
