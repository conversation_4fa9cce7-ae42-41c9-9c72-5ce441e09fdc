import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { 
  ArrowPathIcon,
  DocumentDuplicateIcon,
  ArrowTopRightOnSquareIcon,
  TrashIcon,
  ClockIcon,
  CodeBracketIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlusIcon,
  GlobeAltIcon,
  LinkIcon,
} from '@heroicons/react/24/outline';

export default function ProjectPreviews() {
  const project = useOutletContext<any>();
  const [isCreatingPreview, setIsCreatingPreview] = useState(false);
  const [branchName, setBranchName] = useState('');
  const [previewName, setPreviewName] = useState('');
  
  // Sample preview environments data
  const previews = [
    {
      id: 'prev-1',
      name: 'feature-login',
      url: 'https://feature-login.myshop.poolothost.com',
      branch: 'feature/login',
      commit: 'a1b2c3d',
      author: '<PERSON>',
      status: 'active',
      created: '2 days ago',
      lastDeployed: '6 hours ago',
    },
    {
      id: 'prev-2',
      name: 'feature-checkout',
      url: 'https://feature-checkout.myshop.poolothost.com',
      branch: 'feature/checkout',
      commit: 'e4f5g6h',
      author: 'Jane Smith',
      status: 'active',
      created: '5 days ago',
      lastDeployed: '1 day ago',
    },
    {
      id: 'prev-3',
      name: 'bugfix-cart',
      url: 'https://bugfix-cart.myshop.poolothost.com',
      branch: 'bugfix/cart',
      commit: 'i7j8k9l',
      author: 'John Doe',
      status: 'inactive',
      created: '7 days ago',
      lastDeployed: '3 days ago',
    },
  ];
  
  // Function to create a new preview
  const handleCreatePreview = () => {
    // In a real app, this would call an API to create a new preview
    console.log('Creating preview...');
    setIsCreatingPreview(false);
    setBranchName('');
    setPreviewName('');
    // For demo purposes, we'll just show a success message
    alert('Preview environment created successfully!');
  };
  
  // Function to delete a preview
  const handleDeletePreview = (previewId: string) => {
    // In a real app, this would call an API to delete the preview
    console.log(`Deleting preview ${previewId}...`);
    // For demo purposes, we'll just show a success message
    alert('Preview environment deleted successfully!');
  };
  
  // Function to get status badge
  const getStatusBadge = (status: string) => {
    if (status === 'active') {
      return (
        <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
          <span className="h-1.5 w-1.5 rounded-full bg-green-400 mr-1.5 animate-pulse"></span>
          Active
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
          <span className="h-1.5 w-1.5 rounded-full bg-gray-400 mr-1.5"></span>
          Inactive
        </span>
      );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-white">Preview Environments</h2>
          <p className="text-sm text-gray-400">Create and manage isolated environments for testing features</p>
        </div>
        <Button 
          variant="glass" 
          size="sm"
          icon={<PlusIcon className="h-4 w-4" />}
          onClick={() => setIsCreatingPreview(true)}
        >
          New Preview
        </Button>
      </div>
      
      {isCreatingPreview && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Create Preview Environment</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Preview Name
              </label>
              <input
                type="text"
                className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                  placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                placeholder="e.g., feature-login"
                value={previewName}
                onChange={(e) => setPreviewName(e.target.value)}
              />
              <p className="mt-1 text-xs text-gray-500">This will be used in the preview URL: {previewName ? `${previewName}.${project.name.toLowerCase().replace(/\s+/g, '-')}.poolothost.com` : 'preview-name.myapp.poolothost.com'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Git Branch
              </label>
              <input
                type="text"
                className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                  placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                placeholder="e.g., feature/login"
                value={branchName}
                onChange={(e) => setBranchName(e.target.value)}
              />
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setIsCreatingPreview(false)}
              >
                Cancel
              </Button>
              <Button 
                variant="glass" 
                size="sm"
                icon={<PlusIcon className="h-4 w-4" />}
                onClick={handleCreatePreview}
                disabled={!previewName || !branchName}
              >
                Create Preview
              </Button>
            </div>
          </div>
        </Card>
      )}
      
      <div className="space-y-4">
        {previews.map((preview) => (
          <Card key={preview.id} className="p-6">
            <div className="flex flex-col md:flex-row justify-between gap-4">
              <div>
                <div className="flex items-center mb-2">
                  {getStatusBadge(preview.status)}
                  <h3 className="text-lg font-semibold text-white ml-2">{preview.name}</h3>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-300">
                    <GlobeAltIcon className="h-4 w-4 mr-2 text-gray-400" />
                    <a 
                      href={preview.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-secondary-400 hover:text-secondary-300 transition-colors duration-200"
                    >
                      {preview.url}
                    </a>
                  </div>
                  <div className="flex items-center text-sm text-gray-300">
                    <CodeBracketIcon className="h-4 w-4 mr-2 text-gray-400" />
                    <span className="font-mono">{preview.branch}</span>
                    <span className="mx-2 text-gray-500">•</span>
                    <span className="font-mono">{preview.commit.substring(0, 7)}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-300">
                    <UserIcon className="h-4 w-4 mr-2 text-gray-400" />
                    {preview.author}
                  </div>
                  <div className="flex items-center text-sm text-gray-300">
                    <ClockIcon className="h-4 w-4 mr-2 text-gray-400" />
                    Created {preview.created}
                    <span className="mx-2 text-gray-500">•</span>
                    Last deployed {preview.lastDeployed}
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap gap-2 items-start">
                <Button 
                  variant="outline" 
                  size="sm"
                  as="a"
                  href={preview.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  icon={<ArrowTopRightOnSquareIcon className="h-4 w-4" />}
                >
                  Open
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={<LinkIcon className="h-4 w-4" />}
                  onClick={() => {
                    navigator.clipboard.writeText(preview.url);
                    alert('URL copied to clipboard!');
                  }}
                >
                  Copy URL
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={<ArrowPathIcon className="h-4 w-4" />}
                >
                  Redeploy
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={<TrashIcon className="h-4 w-4" />}
                  onClick={() => handleDeletePreview(preview.id)}
                >
                  Delete
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
      
      {previews.length === 0 && (
        <Card className="p-8 text-center">
          <DocumentDuplicateIcon className="h-12 w-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">No Preview Environments</h3>
          <p className="text-gray-400 mb-6">Create preview environments to test your features in isolation before merging to production.</p>
          <Button 
            variant="glass" 
            icon={<PlusIcon className="h-4 w-4" />}
            onClick={() => setIsCreatingPreview(true)}
          >
            Create First Preview
          </Button>
        </Card>
      )}
    </div>
  );
}
