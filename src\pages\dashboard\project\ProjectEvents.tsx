import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { 
  MagnifyingGlassIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowPathIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

export default function ProjectEvents() {
  const project = useOutletContext<any>();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  
  // Filter events based on search term and status
  const filteredEvents = project.events.filter((event: any) => {
    const matchesSearch = 
      event.message.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || event.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <CheckCircleIcon className="mr-1 h-3 w-3 text-green-400" />
            Success
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
            <XCircleIcon className="h-3 w-3 mr-1 text-red-400" />
            Failed
          </span>
        );
      case 'started':
        return (
          <span className="inline-flex items-center rounded-full bg-blue-900/30 border border-blue-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-blue-300">
            <ClockIcon className="h-3 w-3 mr-1 text-blue-400" />
            Started
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h2 className="text-lg font-semibold text-white">Project Events</h2>
          <p className="text-sm text-gray-400">View deployment history and events</p>
        </div>
        <Button 
          variant="outline" 
          size="sm"
          icon={<ArrowPathIcon className="h-4 w-4" />}
        >
          Refresh
        </Button>
      </div>
      
      <div className="flex flex-col sm:flex-row justify-between gap-4 mb-4">
        <div className="relative flex-grow max-w-md">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10 
              placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
              sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
            placeholder="Search events"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex space-x-2">
          <Button 
            variant={statusFilter === 'all' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('all')}
          >
            All
          </Button>
          <Button 
            variant={statusFilter === 'success' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('success')}
          >
            Success
          </Button>
          <Button 
            variant={statusFilter === 'failed' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('failed')}
          >
            Failed
          </Button>
        </div>
      </div>
      
      <div className="mt-4">
        <div className="space-y-4">
          {filteredEvents.length > 0 ? (
            filteredEvents.map((event: any) => (
              <div 
                key={event.id} 
                className="p-4 rounded-xl bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="mr-3">
                      {getStatusBadge(event.status)}
                    </div>
                    <div>
                      <div className="flex items-center">
                        <p className="text-sm text-white">{event.message}</p>
                        <span className="mx-2 text-gray-500">•</span>
                        <p className="text-sm text-gray-400">{event.type}</p>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">{event.timestamp}</p>
                    </div>
                  </div>
                  {event.status === 'success' && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      icon={<DocumentTextIcon className="h-4 w-4" />}
                    >
                      Logs
                    </Button>
                  )}
                </div>
                {event.details && (
                  <div className="mt-3 pl-10 text-sm text-gray-400">
                    {event.details}
                  </div>
                )}
                {event.status === 'failed' && (
                  <div className="mt-3 flex justify-end">
                    <Button 
                      variant="glass" 
                      size="sm"
                      icon={<ArrowPathIcon className="h-4 w-4" />}
                    >
                      Retry
                    </Button>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="py-8 text-center text-gray-400">
              No events found matching your filters.
            </div>
          )}
        </div>
      </div>
      
      {filteredEvents.length > 0 && (
        <div className="mt-6 flex justify-center">
          <Button variant="outline" size="sm">
            Load More
          </Button>
        </div>
      )}
    </Card>
  );
}
