import { useState } from 'react';
import { Link } from 'react-router-dom';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  RocketLaunchIcon,
  PauseIcon,
  PlayIcon,
  ArrowPathIcon,
  TrashIcon,
  PlusIcon,
  CodeBracketIcon,
  CommandLineIcon,
  CpuChipIcon,
  ClockIcon,
  ServerIcon,
  GlobeAltIcon,
  ArrowLeftIcon,
} from '@heroicons/react/24/outline';

// Sample data for applications
const applications = [
  {
    id: '1',
    name: 'E-commerce Website',
    status: 'running',
    environment: 'production',
    url: 'https://myshop.com',
    lastDeployed: '2 hours ago',
    cpu: '45%',
    memory: '512 MB',
    storage: '2.3 GB',
    framework: 'Next.js',
    region: 'us-east-1',
  },
  {
    id: '2',
    name: 'Blog Platform',
    status: 'running',
    environment: 'staging',
    url: 'https://blog-staging.myapp.io',
    lastDeployed: '1 day ago',
    cpu: '12%',
    memory: '256 MB',
    storage: '1.1 GB',
    framework: 'Gatsby',
    region: 'eu-west-1',
  },
  {
    id: '3',
    name: 'Mobile API',
    status: 'running',
    environment: 'production',
    url: 'https://api.myapp.io',
    lastDeployed: '3 days ago',
    cpu: '28%',
    memory: '1 GB',
    storage: '3.7 GB',
    framework: 'Express.js',
    region: 'us-west-2',
  },
  {
    id: '4',
    name: 'Admin Dashboard',
    status: 'stopped',
    environment: 'development',
    url: 'https://admin-dev.myapp.io',
    lastDeployed: '1 week ago',
    cpu: '0%',
    memory: '0 MB',
    storage: '1.5 GB',
    framework: 'React',
    region: 'ap-southeast-1',
  },
];

export default function Hosting() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEnvironment, setSelectedEnvironment] = useState('all');

  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.url.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesEnvironment = selectedEnvironment === 'all' || app.environment === selectedEnvironment;

    return matchesSearch && matchesEnvironment;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <span className="h-1.5 w-1.5 rounded-full bg-green-400 mr-1.5 animate-pulse"></span>
            Running
          </span>
        );
      case 'stopped':
        return (
          <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
            <PauseIcon className="h-3 w-3 mr-1 text-gray-400" />
            Stopped
          </span>
        );
      case 'deploying':
        return (
          <span className="inline-flex items-center rounded-full bg-blue-900/30 border border-blue-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-blue-300">
            <ArrowPathIcon className="h-3 w-3 mr-1 text-blue-400 animate-spin" />
            Deploying
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <div className="flex items-center mb-2">
            <Button
              variant="outline"
              size="sm"
              as={Link}
              to="/dashboard/services"
              className="mr-3"
              icon={<ArrowLeftIcon className="h-4 w-4" />}
            >
              Back to Services
            </Button>
          </div>
          <h1 className="text-2xl font-bold text-white">Web Hosting Applications</h1>
          <p className="mt-1 text-sm text-gray-400">Manage your deployed web applications with FTP, SSH, and Git access</p>
        </div>
        <Button
          variant="glass"
          glow={true}
          icon={<PlusIcon className="h-5 w-5" />}
        >
          New Application
        </Button>
      </div>

      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          <div className="relative flex-grow max-w-md">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path
                  fillRule="evenodd"
                  d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <input
              type="text"
              className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10
                placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              placeholder="Search applications"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Environment:</span>
            <select
              className="rounded-xl border-0 py-1.5 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10
                focus:ring-2 focus:ring-inset focus:ring-secondary-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              value={selectedEnvironment}
              onChange={(e) => setSelectedEnvironment(e.target.value)}
            >
              <option value="all">All</option>
              <option value="production">Production</option>
              <option value="staging">Staging</option>
              <option value="development">Development</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredApplications.map((app, index) => (
            <Card
              key={app.id}
              className="p-5 relative overflow-hidden border border-white/10 hover:border-secondary-500/50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg hover:shadow-secondary-500/10"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Status indicator line at the top */}
              <div className={`absolute top-0 left-0 right-0 h-1 ${
                app.status === 'running' ? 'bg-green-500' :
                app.status === 'stopped' ? 'bg-gray-500' :
                'bg-blue-500'
              }`}></div>

              <div className="flex justify-between items-start mb-4 pt-2">
                <div>
                  <Link to={`/dashboard/projects/${app.id}`} className="hover:text-secondary-400 transition-colors duration-200">
                    <h3 className="text-lg font-semibold text-white">{app.name}</h3>
                  </Link>
                  <div className="mt-1 flex items-center">
                    {getStatusBadge(app.status)}
                    <span className="ml-2 text-xs text-gray-400">{app.environment}</span>
                  </div>
                </div>
                <div className="flex space-x-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="p-1"
                    title={app.status === 'running' ? 'Stop' : 'Start'}
                  >
                    {app.status === 'running' ? (
                      <PauseIcon className="h-4 w-4" />
                    ) : (
                      <PlayIcon className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="p-1"
                    title="Redeploy"
                  >
                    <ArrowPathIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="mb-4 p-2 bg-white/5 rounded-lg">
                <a
                  href={app.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-secondary-400 hover:text-secondary-300 transition-colors duration-200 flex items-center"
                >
                  <GlobeAltIcon className="h-4 w-4 mr-2" />
                  {app.url}
                </a>
              </div>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="flex items-center p-2 bg-white/5 rounded-lg">
                  <CpuChipIcon className="h-4 w-4 text-secondary-400 mr-2" />
                  <div>
                    <div className="text-xs text-gray-400">CPU</div>
                    <div className="text-sm text-white">{app.cpu}</div>
                  </div>
                </div>
                <div className="flex items-center p-2 bg-white/5 rounded-lg">
                  <CodeBracketIcon className="h-4 w-4 text-secondary-400 mr-2" />
                  <div>
                    <div className="text-xs text-gray-400">Framework</div>
                    <div className="text-sm text-white">{app.framework}</div>
                  </div>
                </div>
                <div className="flex items-center p-2 bg-white/5 rounded-lg">
                  <ServerIcon className="h-4 w-4 text-secondary-400 mr-2" />
                  <div>
                    <div className="text-xs text-gray-400">Memory</div>
                    <div className="text-sm text-white">{app.memory}</div>
                  </div>
                </div>
                <div className="flex items-center p-2 bg-white/5 rounded-lg">
                  <ClockIcon className="h-4 w-4 text-secondary-400 mr-2" />
                  <div>
                    <div className="text-xs text-gray-400">Deployed</div>
                    <div className="text-sm text-white">{app.lastDeployed}</div>
                  </div>
                </div>
              </div>

              <div className="flex justify-between pt-2 border-t border-white/10">
                <Button
                  variant="glass"
                  size="sm"
                  icon={<CommandLineIcon className="h-4 w-4" />}
                  as={Link}
                  to={`/dashboard/projects/${app.id}/shell`}
                >
                  Console
                </Button>
                <Button
                  variant="glass"
                  size="sm"
                  as={Link}
                  to={`/dashboard/projects/${app.id}`}
                >
                  Manage
                </Button>
              </div>
            </Card>
          ))}

          {/* New application card */}
          <Card className="p-5 border border-dashed border-2 border-white/10 flex flex-col items-center justify-center text-center h-full min-h-[250px] hover:border-secondary-500/50 transition-all duration-300 transform hover:-translate-y-1">
            <div className="h-16 w-16 rounded-full bg-secondary-900/50 border border-secondary-500/30 flex items-center justify-center mb-4">
              <PlusIcon className="h-8 w-8 text-secondary-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Deploy New App</h3>
            <p className="text-sm text-gray-400 mb-4">Deploy from Git or upload via FTP/SSH</p>
            <div className="flex flex-col space-y-2">
              <Button
                variant="glass"
                glow={true}
                size="sm"
                icon={<RocketLaunchIcon className="h-4 w-4" />}
              >
                Deploy Now
              </Button>
              <div className="text-xs text-gray-400">
                With FTP, SSH/Putty & Git access
              </div>
            </div>
          </Card>
        </div>
      </Card>
    </div>
  );
}
