import { Link } from 'react-router-dom';
import Layout from '../components/layout/Layout';
import PricingTable from '../components/ui/PricingTable';
import {
  ServerIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  BoltIcon,
  CpuChipIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    name: 'Lightning Fast Deployment',
    description: 'Deploy your applications in seconds with our streamlined workflow.',
    icon: BoltIcon,
  },
  {
    name: 'Global CDN',
    description: 'Deliver content to your users with low latency from our global edge network.',
    icon: GlobeAltIcon,
  },
  {
    name: 'Automatic SSL',
    description: 'Every site gets free SSL certificates automatically provisioned and renewed.',
    icon: ShieldCheckIcon,
  },
  {
    name: 'Scalable Infrastructure',
    description: 'Scale your applications effortlessly as your traffic grows.',
    icon: CpuChipIcon,
  },
  {
    name: 'Continuous Deployment',
    description: 'Automatically deploy your code when you push to your repository.',
    icon: ServerIcon,
  },
  {
    name: '24/7 Monitoring',
    description: 'Keep track of your application\'s health with real-time monitoring.',
    icon: ClockIcon,
  },
];

const pricingTiers = [
  {
    name: 'Free',
    id: 'tier-free',
    price: '$0',
    description: 'Get started with basic hosting for personal projects.',
    features: [
      '512MB RAM',
      '1GB Storage',
      '0.1 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Community Support',
    ],
  },
  {
    name: 'Starter',
    id: 'tier-starter',
    price: '$3.99',
    description: 'Perfect for small projects and personal websites.',
    features: [
      '512MB RAM',
      '2GB Storage',
      '0.5 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Community Support',
    ],
  },
  {
    name: 'Tiny',
    id: 'tier-tiny',
    price: '$5.99',
    description: 'For small production applications.',
    features: [
      '1GB RAM',
      '5GB Storage',
      '1 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Email Support',
      'Custom Domains',
    ],
  },
  {
    name: 'Standard',
    id: 'tier-standard',
    price: '$9.99',
    description: 'For growing applications with more resource needs.',
    features: [
      '2GB RAM',
      '10GB Storage',
      '1 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
    ],
    mostPopular: true,
  },
  {
    name: 'Pro',
    id: 'tier-pro',
    price: '$19.99',
    description: 'For high-traffic applications and business needs.',
    features: [
      '4GB RAM',
      '20GB Storage',
      '2 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
      'Dedicated Instances',
    ],
  },
  {
    name: 'Pro Plus',
    id: 'tier-pro-plus',
    price: '$34.99',
    description: 'For resource-intensive applications.',
    features: [
      '8GB RAM',
      '40GB Storage',
      '4 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
      'Dedicated Instances',
      'Advanced Monitoring',
    ],
  },
  {
    name: 'Pro Max',
    id: 'tier-pro-max',
    price: '$64.99',
    description: 'For high-performance applications.',
    features: [
      '16GB RAM',
      '80GB Storage',
      '4 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
      'Dedicated Instances',
      'Advanced Monitoring',
      'Auto-scaling',
    ],
  },
  {
    name: 'Pro Ultra',
    id: 'tier-pro-ultra',
    price: '$109.99',
    description: 'For enterprise applications with high demands.',
    features: [
      '32GB RAM',
      '160GB Storage',
      '8 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
      'Dedicated Instances',
      'Advanced Monitoring',
      'Auto-scaling',
      'SLA Guarantee',
    ],
  },
  {
    name: 'Custom',
    id: 'tier-custom',
    price: 'Contact',
    description: 'For large-scale enterprise applications with specific requirements.',
    features: [
      '64-512GB RAM',
      '500GB+ Storage',
      '16-64 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
      'Dedicated Instances',
      'Advanced Monitoring',
      'Auto-scaling',
      'SLA Guarantee',
      'Dedicated Account Manager',
    ],
  },
];

export default function Home() {
  return (
    <Layout>
      {/* Hero section */}
      <div className="relative isolate overflow-hidden bg-gradient-to-b from-secondary-100 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="mx-auto max-w-7xl px-6 pb-24 pt-16 sm:pb-32 lg:flex lg:px-8 lg:py-40">
          <div className="mx-auto max-w-2xl lg:mx-0 lg:max-w-xl lg:flex-shrink-0 lg:pt-8">
            <div className="mt-24 sm:mt-32 lg:mt-16">
              <a href="#" className="inline-flex space-x-6">
                <span className="rounded-full bg-secondary-600/10 px-3 py-1 text-sm font-semibold leading-6 text-secondary-600 ring-1 ring-inset ring-secondary-600/10">
                  New features
                </span>
                <span className="inline-flex items-center space-x-2 text-sm font-medium leading-6 text-gray-600 dark:text-gray-300">
                  <span>Just shipped v1.0</span>
                </span>
              </a>
            </div>
            <h1 className="mt-10 text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-6xl">
              Your fastest path to production
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
              Build, deploy, and scale your apps with unparalleled ease — from your first user to your billionth.
            </p>
            <div className="mt-10 flex items-center gap-x-6">
              <Link
                to="/signup"
                className="rounded-md bg-secondary-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-secondary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-secondary-600"
              >
                Get started for free
              </Link>
              <Link to="/docs" className="text-sm font-semibold leading-6 text-gray-900 dark:text-white">
                Learn more <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
          <div className="mx-auto mt-16 flex max-w-2xl sm:mt-24 lg:ml-10 lg:mr-0 lg:mt-0 lg:max-w-none lg:flex-none xl:ml-32">
            <div className="max-w-3xl flex-none sm:max-w-5xl lg:max-w-none">
              <img
                src="https://tailwindui.com/img/component-images/dark-project-app-screenshot.png"
                alt="App screenshot"
                width={2432}
                height={1442}
                className="w-[76rem] rounded-md bg-white/5 shadow-2xl ring-1 ring-white/10"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Feature section */}
      <div className="bg-white dark:bg-gray-900 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-secondary-600">Deploy faster</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
              Everything you need to deploy your app
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
              Focus on your code, not your infrastructure. Our platform handles the heavy lifting so you can build what matters.
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              {features.map((feature) => (
                <div key={feature.name} className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900 dark:text-white">
                    <feature.icon className="h-5 w-5 flex-none text-secondary-600" aria-hidden="true" />
                    {feature.name}
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600 dark:text-gray-300">
                    <p className="flex-auto">{feature.description}</p>
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </div>

      {/* Pricing section */}
      <div className="bg-gray-50 dark:bg-gray-800 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-base font-semibold leading-7 text-secondary-600">Pricing</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
              Simple, transparent pricing
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
              Choose the plan that's right for you. All plans include a 14-day free trial.
            </p>
          </div>
          <div className="mt-16">
            <PricingTable tiers={pricingTiers} />
            <div className="mt-12 text-center">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Need more resources?</h3>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                For custom requirements or resources beyond Pro Ultra (64-512GB RAM, 16-64 CPU, 500GB+ storage),
                <br />please contact our support team for a tailored solution.
              </p>
              <div className="mt-6">
                <a
                  href="#"
                  className="rounded-md bg-secondary-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-secondary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-secondary-600"
                >
                  Contact Support
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="bg-white dark:bg-gray-900">
        <div className="mx-auto max-w-7xl py-24 sm:px-6 sm:py-32 lg:px-8">
          <div className="relative isolate overflow-hidden bg-gray-900 px-6 py-24 text-center shadow-2xl sm:rounded-3xl sm:px-16">
            <h2 className="mx-auto max-w-2xl text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Ready to get started?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-300">
              Join thousands of developers who are already building and deploying with PoolotHost.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                to="/signup"
                className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-gray-900 shadow-sm hover:bg-gray-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
              >
                Get started
              </Link>
              <Link to="/contact" className="text-sm font-semibold leading-6 text-white">
                Contact sales <span aria-hidden="true">→</span>
              </Link>
            </div>
            <svg
              viewBox="0 0 1024 1024"
              className="absolute left-1/2 top-1/2 -z-10 h-[64rem] w-[64rem] -translate-x-1/2 -translate-y-1/2 [mask-image:radial-gradient(closest-side,white,transparent)]"
              aria-hidden="true"
            >
              <circle cx={512} cy={512} r={512} fill="url(#827591b1-ce8c-4110-b064-7cb85a0b1217)" fillOpacity="0.7" />
              <defs>
                <radialGradient id="827591b1-ce8c-4110-b064-7cb85a0b1217">
                  <stop stopColor="#7775D6" />
                  <stop offset={1} stopColor="#E935C1" />
                </radialGradient>
              </defs>
            </svg>
          </div>
        </div>
      </div>
    </Layout>
  );
}
