import { useState } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  GlobeAltIcon,
  ShieldCheckIcon,
  CreditCardIcon,
  EnvelopeIcon,
  ServerIcon,
  CloudIcon,
  BellIcon,
  DocumentTextIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';

export default function AdminSettings() {
  const [activeTab, setActiveTab] = useState('general');
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">System Settings</h1>
          <p className="mt-1 text-sm text-gray-400">Configure global settings for the platform</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Card className="p-6 sticky top-24">
            <nav className="space-y-1">
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'general'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('general')}
              >
                <GlobeAltIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                General
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'security'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('security')}
              >
                <ShieldCheckIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Security
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'billing'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('billing')}
              >
                <CreditCardIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Billing
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'email'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('email')}
              >
                <EnvelopeIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Email
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'servers'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('servers')}
              >
                <ServerIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Servers
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'maintenance'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('maintenance')}
              >
                <ArrowPathIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Maintenance
              </button>
            </nav>
          </Card>
        </div>
        
        {/* Main content */}
        <div className="lg:col-span-3">
          {activeTab === 'general' && (
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">General Settings</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-md font-medium text-white mb-4">Platform Information</h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="site-name" className="block text-sm font-medium text-gray-300">
                        Site Name
                      </label>
                      <input
                        type="text"
                        name="site-name"
                        id="site-name"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="PoolotHost"
                      />
                    </div>
                    <div>
                      <label htmlFor="site-url" className="block text-sm font-medium text-gray-300">
                        Site URL
                      </label>
                      <input
                        type="url"
                        name="site-url"
                        id="site-url"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="https://poolothost.com"
                      />
                    </div>
                    <div>
                      <label htmlFor="admin-email" className="block text-sm font-medium text-gray-300">
                        Admin Email
                      </label>
                      <input
                        type="email"
                        name="admin-email"
                        id="admin-email"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="<EMAIL>"
                      />
                    </div>
                    <div>
                      <label htmlFor="support-email" className="block text-sm font-medium text-gray-300">
                        Support Email
                      </label>
                      <input
                        type="email"
                        name="support-email"
                        id="support-email"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="<EMAIL>"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="pt-6 border-t border-white/10">
                  <h3 className="text-md font-medium text-white mb-4">Regional Settings</h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="timezone" className="block text-sm font-medium text-gray-300">
                        Default Timezone
                      </label>
                      <select
                        id="timezone"
                        name="timezone"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          focus:ring-2 focus:ring-inset focus:ring-red-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="UTC"
                      >
                        <option value="UTC">UTC</option>
                        <option value="America/Los_Angeles">Pacific Time (US & Canada)</option>
                        <option value="America/New_York">Eastern Time (US & Canada)</option>
                        <option value="Europe/London">London</option>
                        <option value="Asia/Tokyo">Tokyo</option>
                      </select>
                    </div>
                    <div>
                      <label htmlFor="date-format" className="block text-sm font-medium text-gray-300">
                        Date Format
                      </label>
                      <select
                        id="date-format"
                        name="date-format"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          focus:ring-2 focus:ring-inset focus:ring-red-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="MM/DD/YYYY"
                      >
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      </select>
                    </div>
                  </div>
                </div>
                
                <div className="pt-6 border-t border-white/10">
                  <h3 className="text-md font-medium text-white mb-4">Feature Toggles</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="enable-blog"
                          name="enable-blog"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-600"
                          defaultChecked
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="enable-blog" className="font-medium text-gray-300">
                          Enable Blog
                        </label>
                        <p className="text-gray-500">Show blog section on the website</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="enable-docs"
                          name="enable-docs"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-600"
                          defaultChecked
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="enable-docs" className="font-medium text-gray-300">
                          Enable Documentation
                        </label>
                        <p className="text-gray-500">Show documentation section on the website</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="enable-signup"
                          name="enable-signup"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-600"
                          defaultChecked
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="enable-signup" className="font-medium text-gray-300">
                          Enable User Registration
                        </label>
                        <p className="text-gray-500">Allow new users to sign up</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button variant="outline" className="mr-3">
                    Cancel
                  </Button>
                  <Button variant="glass" glow={true}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </Card>
          )}
          
          {activeTab === 'security' && (
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">Security Settings</h2>
              
              <div className="space-y-8">
                <div>
                  <h3 className="text-md font-medium text-white mb-4">Authentication</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="require-2fa"
                          name="require-2fa"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-600"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="require-2fa" className="font-medium text-gray-300">
                          Require Two-Factor Authentication
                        </label>
                        <p className="text-gray-500">Force all users to set up 2FA</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="password-expiry"
                          name="password-expiry"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-600"
                          defaultChecked
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="password-expiry" className="font-medium text-gray-300">
                          Password Expiry
                        </label>
                        <p className="text-gray-500">Force password reset every 90 days</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="min-password-length" className="block text-sm font-medium text-gray-300">
                        Minimum Password Length
                      </label>
                      <input
                        type="number"
                        name="min-password-length"
                        id="min-password-length"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="8"
                        min="8"
                        max="32"
                      />
                    </div>
                    <div>
                      <label htmlFor="max-login-attempts" className="block text-sm font-medium text-gray-300">
                        Max Login Attempts
                      </label>
                      <input
                        type="number"
                        name="max-login-attempts"
                        id="max-login-attempts"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="5"
                        min="1"
                        max="10"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="pt-6 border-t border-white/10">
                  <h3 className="text-md font-medium text-white mb-4">Session Management</h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="session-timeout" className="block text-sm font-medium text-gray-300">
                        Session Timeout (minutes)
                      </label>
                      <input
                        type="number"
                        name="session-timeout"
                        id="session-timeout"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="60"
                        min="5"
                      />
                    </div>
                    <div>
                      <label htmlFor="remember-me-duration" className="block text-sm font-medium text-gray-300">
                        "Remember Me" Duration (days)
                      </label>
                      <input
                        type="number"
                        name="remember-me-duration"
                        id="remember-me-duration"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        defaultValue="30"
                        min="1"
                        max="365"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="pt-6 border-t border-white/10">
                  <h3 className="text-md font-medium text-white mb-4">API Security</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="api-rate-limiting"
                          name="api-rate-limiting"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-600"
                          defaultChecked
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="api-rate-limiting" className="font-medium text-gray-300">
                          API Rate Limiting
                        </label>
                        <p className="text-gray-500">Limit API requests to prevent abuse</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="api-key-expiry"
                          name="api-key-expiry"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-600"
                          defaultChecked
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="api-key-expiry" className="font-medium text-gray-300">
                          API Key Expiry
                        </label>
                        <p className="text-gray-500">Force API key rotation every 90 days</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button variant="outline" className="mr-3">
                    Cancel
                  </Button>
                  <Button variant="glass" glow={true}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </Card>
          )}
          
          {activeTab === 'maintenance' && (
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">Maintenance Mode</h2>
              
              <div className="space-y-6">
                <div className="p-6 rounded-xl bg-white/5 border border-white/10">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-md font-medium text-white">Maintenance Mode</h3>
                      <p className="mt-1 text-sm text-gray-400">
                        When enabled, the site will display a maintenance message to all non-admin users.
                      </p>
                    </div>
                    <div className="flex h-6 items-center">
                      <input
                        id="maintenance-mode"
                        name="maintenance-mode"
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-600"
                      />
                    </div>
                  </div>
                </div>
                
                <div>
                  <label htmlFor="maintenance-message" className="block text-sm font-medium text-gray-300">
                    Maintenance Message
                  </label>
                  <textarea
                    name="maintenance-message"
                    id="maintenance-message"
                    rows={4}
                    className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                      placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                      sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                    defaultValue="We're currently performing scheduled maintenance. Please check back soon."
                  />
                </div>
                
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="maintenance-start" className="block text-sm font-medium text-gray-300">
                      Start Time
                    </label>
                    <input
                      type="datetime-local"
                      name="maintenance-start"
                      id="maintenance-start"
                      className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                        placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                    />
                  </div>
                  <div>
                    <label htmlFor="maintenance-end" className="block text-sm font-medium text-gray-300">
                      End Time
                    </label>
                    <input
                      type="datetime-local"
                      name="maintenance-end"
                      id="maintenance-end"
                      className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                        placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                    />
                  </div>
                </div>
                
                <div className="pt-6 border-t border-white/10">
                  <h3 className="text-md font-medium text-white mb-4">System Backup</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="flex h-5 items-center">
                        <input
                          id="auto-backup"
                          name="auto-backup"
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-600"
                          defaultChecked
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="auto-backup" className="font-medium text-gray-300">
                          Automatic Backups
                        </label>
                        <p className="text-gray-500">Schedule regular system backups</p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                      <div>
                        <label htmlFor="backup-frequency" className="block text-sm font-medium text-gray-300">
                          Backup Frequency
                        </label>
                        <select
                          id="backup-frequency"
                          name="backup-frequency"
                          className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                            focus:ring-2 focus:ring-inset focus:ring-red-500 
                            sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                          defaultValue="daily"
                        >
                          <option value="hourly">Hourly</option>
                          <option value="daily">Daily</option>
                          <option value="weekly">Weekly</option>
                          <option value="monthly">Monthly</option>
                        </select>
                      </div>
                      <div>
                        <label htmlFor="backup-retention" className="block text-sm font-medium text-gray-300">
                          Backup Retention (days)
                        </label>
                        <input
                          type="number"
                          name="backup-retention"
                          id="backup-retention"
                          className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                            placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500 
                            sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                          defaultValue="30"
                          min="1"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-4 flex justify-end">
                    <Button 
                      variant="glass" 
                      icon={<ArrowPathIcon className="h-5 w-5" />}
                    >
                      Run Backup Now
                    </Button>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button variant="outline" className="mr-3">
                    Cancel
                  </Button>
                  <Button variant="glass" glow={true}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
