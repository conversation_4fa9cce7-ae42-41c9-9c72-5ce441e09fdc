import { useState, useRef, useEffect } from 'react';
import { CheckCircleIcon, XCircleIcon, PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/20/solid';
import { ClockIcon } from '@heroicons/react/24/outline';
import Card from '../ui/Card';
import Button from '../ui/Button';

interface Domain {
  id: string;
  name: string;
  status: 'active' | 'pending' | 'expired';
  expiresAt: string;
  autoRenew: boolean;
}

interface DomainListProps {
  domains: Domain[];
}

export default function DomainList({ domains }: DomainListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [hoveredRow, setHoveredRow] = useState<string | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const filteredDomains = domains.filter(domain =>
    domain.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Focus search input with keyboard shortcut
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+K or Cmd+K
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        searchInputRef.current?.focus();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <CheckCircleIcon className="mr-1 h-4 w-4 text-green-400" />
            Active
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-900/30 border border-yellow-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-yellow-300">
            <ClockIcon className="mr-1 h-4 w-4 text-yellow-400" />
            Pending
          </span>
        );
      case 'expired':
        return (
          <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
            <XCircleIcon className="mr-1 h-4 w-4 text-red-400" />
            Expired
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute -z-10 -top-20 -right-20 w-64 h-64 bg-secondary-600/5 rounded-full blur-3xl"></div>
      <div className="absolute -z-10 -bottom-20 -left-20 w-64 h-64 bg-primary-600/5 rounded-full blur-3xl"></div>

      <div className="px-4 sm:px-6 lg:px-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-xl font-semibold leading-6 text-white">Domains</h1>
            <p className="mt-2 text-sm text-gray-300">
              A list of all domains in your account including their status and expiration date.
            </p>
          </div>
          <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <Button
              variant="glass"
              glow={true}
              icon={<PlusIcon className="h-5 w-5" />}
            >
              Add domain
            </Button>
          </div>
        </div>
        <div className="mt-6">
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              ref={searchInputRef}
              type="text"
              className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10
                placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              placeholder="Search domains (Ctrl+K)"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <div className="mt-8 flow-root">
          <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
              <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
                <table className="min-w-full divide-y divide-white/10">
                  <thead>
                    <tr>
                      <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                        Domain Name
                      </th>
                      <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                        Status
                      </th>
                      <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                        Expires
                      </th>
                      <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                        Auto Renew
                      </th>
                      <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-white/10">
                    {filteredDomains.length > 0 ? (
                      filteredDomains.map((domain, index) => (
                        <tr
                          key={domain.id}
                          className={`transition-colors duration-200 ${hoveredRow === domain.id ? 'bg-white/10' : ''}`}
                          onMouseEnter={() => setHoveredRow(domain.id)}
                          onMouseLeave={() => setHoveredRow(null)}
                          style={{ animationDelay: `${index * 50}ms` }}
                        >
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                            {domain.name}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                            {getStatusBadge(domain.status)}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                            {domain.expiresAt}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm">
                            {domain.autoRenew ? (
                              <span className="text-green-400 font-medium">Yes</span>
                            ) : (
                              <span className="text-red-400 font-medium">No</span>
                            )}
                          </td>
                          <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                            <Button
                              variant="ghost"
                              size="sm"
                              className={`transition-opacity duration-300 ${hoveredRow === domain.id ? 'opacity-100' : 'opacity-70'}`}
                            >
                              Manage
                              <span className="sr-only">, {domain.name}</span>
                            </Button>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="py-8 text-center text-sm text-gray-400">
                          <div className="flex flex-col items-center justify-center space-y-3">
                            <MagnifyingGlassIcon className="h-10 w-10 text-gray-500/50" />
                            <p>No domains found matching your search</p>
                            {searchTerm && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSearchTerm('')}
                              >
                                Clear search
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
