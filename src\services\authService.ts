import { apiRequest, type ApiResponse } from './api';
import { type AuthResponse, type User } from '../stores/authStore';

// Auth request/response types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  first_name: string;
  last_name: string;
  password: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface LoginResponse {
  token: string;
  user: User;
  expires_at: string;
}

export interface RegisterResponse {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  company: string | null;
  role: string;
  status: string;
  created_at: string;
}

export interface RefreshTokenResponse {
  token: string;
  expires_at: string;
}

class AuthService {
  /**
   * <PERSON>gin user with email and password
   */
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await apiRequest<AuthResponse>({
        method: 'POST',
        url: '/public/auth/login',
        data: credentials,
      });

      console.log('🔐 Login successful:', response.data.user.email);
      return response;
    } catch (error: any) {
      console.error('❌ Login failed:', error.response?.data || error.message);
      throw new Error(
        error.response?.data?.error?.message || 
        'Login failed. Please check your credentials.'
      );
    }
  }

  /**
   * Register new user
   */
  async register(userData: RegisterRequest): Promise<ApiResponse<RegisterResponse>> {
    try {
      const response = await apiRequest<ApiResponse<RegisterResponse>>({
        method: 'POST',
        url: '/public/auth/register',
        data: userData,
      });

      console.log('✅ Registration successful:', response.data.email);
      return response;
    } catch (error: any) {
      console.error('❌ Registration failed:', error.response?.data || error.message);
      throw new Error(
        error.response?.data?.error?.message ||
        'Registration failed. Please try again.'
      );
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken: string): Promise<ApiResponse<RefreshTokenResponse>> {
    try {
      const response = await apiRequest<ApiResponse<RefreshTokenResponse>>({
        method: 'POST',
        url: '/public/auth/refresh',
        data: { refresh_token: refreshToken },
      });

      console.log('🔄 Token refreshed successfully');
      return response;
    } catch (error: any) {
      console.error('❌ Token refresh failed:', error.response?.data || error.message);
      throw new Error(
        error.response?.data?.error?.message || 
        'Token refresh failed. Please login again.'
      );
    }
  }

  /**
   * Logout user (client-side only for now)
   */
  async logout(): Promise<void> {
    try {
      // If you have a logout endpoint, call it here
      // await apiRequest({
      //   method: 'POST',
      //   url: '/auth/logout',
      // });

      console.log('👋 Logout successful');
    } catch (error: any) {
      console.error('❌ Logout error:', error.response?.data || error.message);
      // Don't throw error for logout, just log it
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const response = await apiRequest<ApiResponse<User>>({
        method: 'GET',
        url: '/auth/me',
      });

      console.log('👤 User profile fetched:', response.data.email);
      return response;
    } catch (error: any) {
      console.error('❌ Failed to fetch user profile:', error.response?.data || error.message);
      throw new Error(
        error.response?.data?.error?.message || 
        'Failed to fetch user profile.'
      );
    }
  }

  /**
   * Validate token expiration
   */
  isTokenValid(expiresAt: string): boolean {
    const expirationTime = new Date(expiresAt).getTime();
    const currentTime = new Date().getTime();
    const bufferTime = 5 * 60 * 1000; // 5 minutes buffer
    
    return currentTime < (expirationTime - bufferTime);
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
