import { useState } from 'react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  EyeSlashIcon,
  PencilIcon,
  TrashIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';

interface EnvVar {
  key: string;
  value: string;
}

interface ServiceEnvironmentProps {
  environment: EnvVar[];
}

export default function ServiceEnvironment({ environment }: ServiceEnvironmentProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [showValues, setShowValues] = useState<Record<string, boolean>>({});

  // Filter environment variables based on search term
  const filteredEnvVars = Array.isArray(environment)
    ? environment.filter(env =>
        env.key.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : [];

  const toggleValueVisibility = (key: string) => {
    setShowValues(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-white">Environment Variables</h2>
            <p className="text-sm text-gray-400">Configure environment-specific config and secrets</p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              icon={<ArrowPathIcon className="h-4 w-4" />}
            >
              Refresh
            </Button>
            <Button
              variant="glass"
              size="sm"
              icon={<PlusIcon className="h-4 w-4" />}
            >
              Add Variable
            </Button>
          </div>
        </div>

        <div className="relative flex-grow max-w-md mb-6">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10
              placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
              sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
            placeholder="Search environment variables"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
          <table className="min-w-full divide-y divide-white/10">
            <thead>
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                  Key
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Value
                </th>
                <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {filteredEnvVars.length > 0 ? (
                filteredEnvVars.map((env) => (
                  <tr key={env.key} className="hover:bg-white/5 transition-colors duration-200">
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                      {env.key}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      <div className="flex items-center">
                        <span className="font-mono">
                          {showValues[env.key] ? env.value : '••••••••••••'}
                        </span>
                        <button
                          type="button"
                          className="ml-2 text-gray-400 hover:text-white transition-colors duration-200"
                          onClick={() => toggleValueVisibility(env.key)}
                        >
                          {showValues[env.key] ? (
                            <EyeSlashIcon className="h-4 w-4" />
                          ) : (
                            <EyeIcon className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </td>
                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="p-1"
                          title="Edit"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="p-1"
                          title="Delete"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} className="py-8 text-center text-gray-400">
                    No environment variables found matching your search.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-white">Secret Files</h2>
            <p className="text-sm text-gray-400">Store plaintext files containing secret data</p>
          </div>
          <Button
            variant="glass"
            size="sm"
            icon={<PlusIcon className="h-4 w-4" />}
          >
            Add Secret File
          </Button>
        </div>

        <div className="p-8 text-center border border-dashed border-white/20 rounded-xl">
          <p className="text-gray-400 mb-4">No secret files have been added yet.</p>
          <Button
            variant="outline"
            size="sm"
            icon={<PlusIcon className="h-4 w-4" />}
          >
            Add Secret File
          </Button>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-white">Linked Environment Groups</h2>
            <p className="text-sm text-gray-400">Share environment variables across multiple services</p>
          </div>
          <Button
            variant="glass"
            size="sm"
            icon={<PlusIcon className="h-4 w-4" />}
          >
            New Environment Group
          </Button>
        </div>

        <div className="p-8 text-center border border-dashed border-white/20 rounded-xl">
          <p className="text-gray-400 mb-4">No environment groups available to link.</p>
          <Button
            variant="outline"
            size="sm"
            icon={<PlusIcon className="h-4 w-4" />}
          >
            New Environment Group
          </Button>
        </div>
      </Card>
    </div>
  );
}
