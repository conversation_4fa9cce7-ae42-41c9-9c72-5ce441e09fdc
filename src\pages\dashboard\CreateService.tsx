import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  GlobeAltIcon,
  ServerIcon,
  CircleStackIcon,
  CodeBracketIcon,
  DocumentTextIcon,
  CloudIcon,
  ArrowLeftIcon,
  FolderIcon,
  LinkIcon,
  CubeIcon,
} from '@heroicons/react/24/outline';

type ServiceType = 'web-service' | 'static-site' | 'database' | 'background-worker' | 'cron-job';
type SourceType = 'git' | 'upload' | 'existing-image';

const serviceTypes = [
  {
    id: 'web-service' as ServiceType,
    name: 'Web Service',
    description: 'Deploy web applications, APIs, and backend services',
    icon: GlobeAltIcon,
    features: ['Auto-scaling', 'Load balancing', 'SSL certificates', 'Custom domains'],
    examples: ['Node.js API', 'Python Flask', 'Rust Actix', 'Go Gin'],
  },
  {
    id: 'static-site' as ServiceType,
    name: 'Static Site',
    description: 'Deploy static websites and single-page applications',
    icon: DocumentTextIcon,
    features: ['CDN distribution', 'Instant deploys', 'Branch previews', 'Form handling'],
    examples: ['React SPA', 'Vue.js app', 'HTML/CSS site', 'Jekyll blog'],
  },
  {
    id: 'database' as ServiceType,
    name: 'Database',
    description: 'Managed database instances with automatic backups',
    icon: CircleStackIcon,
    features: ['Automatic backups', 'High availability', 'Monitoring', 'Scaling'],
    examples: ['PostgreSQL', 'MySQL', 'MongoDB', 'Redis'],
  },
  {
    id: 'background-worker' as ServiceType,
    name: 'Background Worker',
    description: 'Long-running background processes and job queues',
    icon: ServerIcon,
    features: ['Auto-restart', 'Resource monitoring', 'Log aggregation', 'Health checks'],
    examples: ['Queue processor', 'Data pipeline', 'Email service', 'File processor'],
  },
  {
    id: 'cron-job' as ServiceType,
    name: 'Cron Job',
    description: 'Scheduled tasks and periodic job execution',
    icon: CloudIcon,
    features: ['Flexible scheduling', 'Retry logic', 'Notifications', 'Timeout handling'],
    examples: ['Daily reports', 'Data sync', 'Cleanup tasks', 'Health checks'],
  },
];

const sourceOptions = [
  {
    id: 'git' as SourceType,
    name: 'Git Repository',
    description: 'Deploy from GitHub, GitLab, or Bitbucket',
    icon: CodeBracketIcon,
    popular: true,
  },
  {
    id: 'upload' as SourceType,
    name: 'Upload Files',
    description: 'Upload your code directly via file manager',
    icon: FolderIcon,
    popular: false,
  },
  {
    id: 'existing-image' as SourceType,
    name: 'Docker Image',
    description: 'Deploy from existing Docker image',
    icon: CubeIcon,
    popular: false,
  },
];

export default function CreateService() {
  const navigate = useNavigate();
  const [selectedServiceType, setSelectedServiceType] = useState<ServiceType | null>(null);
  const [selectedSourceType, setSelectedSourceType] = useState<SourceType | null>(null);
  const [step, setStep] = useState(1);

  const handleServiceTypeSelect = (type: ServiceType) => {
    setSelectedServiceType(type);
    setStep(2);
  };

  const handleSourceTypeSelect = (type: SourceType) => {
    setSelectedSourceType(type);
    setStep(3);
  };

  const handleBack = () => {
    if (step === 1) {
      navigate('/dashboard/services');
    } else {
      setStep(step - 1);
      if (step === 2) {
        setSelectedServiceType(null);
      } else if (step === 3) {
        setSelectedSourceType(null);
      }
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      <div className="flex items-center space-x-4">
        <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
          step >= 1 ? 'bg-secondary-500 text-white' : 'bg-gray-700 text-gray-400'
        }`}>
          1
        </div>
        <div className={`w-12 h-0.5 ${step >= 2 ? 'bg-secondary-500' : 'bg-gray-700'}`} />
        <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
          step >= 2 ? 'bg-secondary-500 text-white' : 'bg-gray-700 text-gray-400'
        }`}>
          2
        </div>
        <div className={`w-12 h-0.5 ${step >= 3 ? 'bg-secondary-500' : 'bg-gray-700'}`} />
        <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
          step >= 3 ? 'bg-secondary-500 text-white' : 'bg-gray-700 text-gray-400'
        }`}>
          3
        </div>
      </div>
    </div>
  );

  const renderServiceTypeSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">What would you like to deploy?</h2>
        <p className="text-gray-400">Choose the type of service you want to create</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {serviceTypes.map((service) => {
          const Icon = service.icon;
          return (
            <Card
              key={service.id}
              className={`p-6 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-secondary-500/20 ${
                selectedServiceType === service.id
                  ? 'border-secondary-500 bg-secondary-500/10'
                  : 'border-white/10 hover:border-secondary-500/50'
              }`}
              onClick={() => handleServiceTypeSelect(service.id)}
            >
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-3 rounded-full bg-secondary-500/20">
                  <Icon className="h-8 w-8 text-secondary-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">{service.name}</h3>
                  <p className="text-sm text-gray-400 mb-4">{service.description}</p>
                </div>
                <div className="space-y-2 w-full">
                  <h4 className="text-xs font-medium text-gray-300 uppercase tracking-wide">Features</h4>
                  <ul className="text-xs text-gray-400 space-y-1">
                    {service.features.slice(0, 3).map((feature, index) => (
                      <li key={index}>• {feature}</li>
                    ))}
                  </ul>
                </div>
                <div className="space-y-2 w-full">
                  <h4 className="text-xs font-medium text-gray-300 uppercase tracking-wide">Examples</h4>
                  <div className="flex flex-wrap gap-1">
                    {service.examples.slice(0, 2).map((example, index) => (
                      <span
                        key={index}
                        className="inline-block px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded"
                      >
                        {example}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );

  const renderSourceTypeSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">How do you want to deploy?</h2>
        <p className="text-gray-400">Choose your deployment source</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        {sourceOptions.map((source) => {
          const Icon = source.icon;
          return (
            <Card
              key={source.id}
              className={`p-6 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-secondary-500/20 relative ${
                selectedSourceType === source.id
                  ? 'border-secondary-500 bg-secondary-500/10'
                  : 'border-white/10 hover:border-secondary-500/50'
              }`}
              onClick={() => handleSourceTypeSelect(source.id)}
            >
              {source.popular && (
                <div className="absolute -top-2 -right-2 bg-secondary-500 text-white text-xs px-2 py-1 rounded-full">
                  Popular
                </div>
              )}
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-3 rounded-full bg-secondary-500/20">
                  <Icon className="h-8 w-8 text-secondary-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">{source.name}</h3>
                  <p className="text-sm text-gray-400">{source.description}</p>
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBack}
            icon={<ArrowLeftIcon className="h-4 w-4" />}
          >
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-white">Create New Service</h1>
            <p className="text-sm text-gray-400">Deploy your application to PoolotHost</p>
          </div>
        </div>
      </div>

      {renderStepIndicator()}

      <Card className="p-8">
        {step === 1 && renderServiceTypeSelection()}
        {step === 2 && renderSourceTypeSelection()}
        {step === 3 && (
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-4">Configure Your Service</h2>
            <p className="text-gray-400 mb-8">
              Selected: {serviceTypes.find(s => s.id === selectedServiceType)?.name} via{' '}
              {sourceOptions.find(s => s.id === selectedSourceType)?.name}
            </p>
            <Button
              variant="glass"
              glow={true}
              onClick={() => {
                // Navigate to the appropriate configuration page based on selections
                navigate(`/dashboard/services/configure?type=${selectedServiceType}&source=${selectedSourceType}`);
              }}
            >
              Continue to Configuration
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
}
