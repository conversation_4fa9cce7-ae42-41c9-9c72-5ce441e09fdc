import { Fragment, useState } from 'react';
import { Menu, Transition } from '@headlessui/react';
import { BellIcon, CheckIcon } from '@heroicons/react/24/outline';

// Sample notifications data
const notifications = [
  {
    id: '1',
    title: 'Deployment successful',
    description: 'Your application has been deployed successfully.',
    time: '3 hours ago',
    read: false,
  },
  {
    id: '2',
    title: 'Domain renewed',
    description: 'Your domain myapp.io has been renewed for another year.',
    time: 'Yesterday',
    read: false,
  },
  {
    id: '3',
    title: 'SSL certificate issued',
    description: 'SSL certificate for test-site.dev has been issued.',
    time: '2 days ago',
    read: true,
  },
  {
    id: '4',
    title: 'Storage limit reached',
    description: 'You are approaching your storage limit. Consider upgrading your plan.',
    time: '5 days ago',
    read: true,
  },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function NotificationsMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const [notificationsList, setNotificationsList] = useState(notifications);
  
  const unreadCount = notificationsList.filter(notification => !notification.read).length;
  
  const markAllAsRead = () => {
    setNotificationsList(notificationsList.map(notification => ({
      ...notification,
      read: true,
    })));
  };
  
  const markAsRead = (id: string) => {
    setNotificationsList(notificationsList.map(notification => 
      notification.id === id ? { ...notification, read: true } : notification
    ));
  };

  return (
    <Menu as="div" className="relative">
      <Menu.Button 
        className="relative rounded-full bg-white/10 p-2 text-white hover:bg-white/20 transition-colors duration-200"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="sr-only">View notifications</span>
        <BellIcon className="h-6 w-6" aria-hidden="true" />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-gray-900"></span>
        )}
      </Menu.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-200"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-150"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
        afterEnter={() => setIsOpen(true)}
        afterLeave={() => setIsOpen(false)}
      >
        <Menu.Items className="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-xl bg-gray-900/90 backdrop-blur-xl border border-white/10 py-1 shadow-lg ring-1 ring-white/5 focus:outline-none">
          <div className="flex items-center justify-between px-4 py-3 border-b border-white/10">
            <h3 className="text-sm font-medium text-white">Notifications</h3>
            {unreadCount > 0 && (
              <button
                type="button"
                className="text-xs text-secondary-400 hover:text-secondary-300 transition-colors duration-200"
                onClick={markAllAsRead}
              >
                Mark all as read
              </button>
            )}
          </div>
          <div className="max-h-96 overflow-y-auto">
            {notificationsList.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-sm text-gray-400">No notifications</p>
              </div>
            ) : (
              <div className="divide-y divide-white/10">
                {notificationsList.map((notification) => (
                  <Menu.Item key={notification.id}>
                    {({ active }) => (
                      <div
                        className={classNames(
                          active ? 'bg-white/10' : '',
                          notification.read ? 'opacity-70' : '',
                          'px-4 py-3 cursor-pointer transition-colors duration-200'
                        )}
                        onClick={() => markAsRead(notification.id)}
                      >
                        <div className="flex justify-between">
                          <p className="text-sm font-medium text-white">{notification.title}</p>
                          {!notification.read && (
                            <span className="ml-2 flex-shrink-0">
                              <span className="inline-block h-2 w-2 rounded-full bg-secondary-500"></span>
                            </span>
                          )}
                        </div>
                        <p className="mt-1 text-xs text-gray-400">{notification.description}</p>
                        <p className="mt-1 text-xs text-gray-500">{notification.time}</p>
                      </div>
                    )}
                  </Menu.Item>
                ))}
              </div>
            )}
          </div>
          <div className="border-t border-white/10 px-4 py-2">
            <a
              href="#"
              className="block text-center text-xs text-secondary-400 hover:text-secondary-300 transition-colors duration-200"
            >
              View all notifications
            </a>
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
