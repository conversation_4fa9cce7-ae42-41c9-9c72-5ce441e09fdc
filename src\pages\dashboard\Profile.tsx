import { useState } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  UserCircleIcon,
  KeyIcon,
  ShieldCheckIcon,
  BellIcon,
  EnvelopeIcon,
  DevicePhoneMobileIcon,
} from '@heroicons/react/24/outline';

export default function Profile() {
  const [activeTab, setActiveTab] = useState('general');
  
  // Mock user data
  const user = {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: null, // If null, we'll show initials
    role: 'Developer',
    company: 'Acme Inc.',
    location: 'San Francisco, CA',
    timezone: 'Pacific Time (US & Canada)',
    bio: 'Full-stack developer with a passion for building scalable web applications.',
    website: 'https://johndoe.dev',
    github: 'johndoe',
    twitter: 'johndoe',
    linkedin: 'johndoe',
  };
  
  // Get user initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Profile Settings</h1>
          <p className="mt-1 text-sm text-gray-400">Manage your account settings and preferences</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Card className="p-6 sticky top-24">
            <div className="flex flex-col items-center text-center mb-6">
              {user.avatar ? (
                <img
                  className="h-24 w-24 rounded-full mb-4"
                  src={user.avatar}
                  alt={user.name}
                />
              ) : (
                <div className="h-24 w-24 rounded-full bg-secondary-600 flex items-center justify-center text-white text-2xl font-medium mb-4">
                  {getInitials(user.name)}
                </div>
              )}
              <h2 className="text-lg font-semibold text-white">{user.name}</h2>
              <p className="text-sm text-gray-400">{user.email}</p>
              <p className="text-xs text-gray-500 mt-1">{user.role} at {user.company}</p>
            </div>
            
            <nav className="space-y-1">
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'general'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('general')}
              >
                <UserCircleIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                General
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'security'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('security')}
              >
                <ShieldCheckIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Security
              </button>
              <button
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'notifications'
                    ? 'bg-white/10 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
                onClick={() => setActiveTab('notifications')}
              >
                <BellIcon className="mr-3 h-5 w-5 flex-shrink-0" />
                Notifications
              </button>
            </nav>
          </Card>
        </div>
        
        {/* Main content */}
        <div className="lg:col-span-3">
          {activeTab === 'general' && (
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">General Information</h2>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-300">
                      Full Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      id="name"
                      className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                        placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                      defaultValue={user.name}
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                      Email Address
                    </label>
                    <input
                      type="email"
                      name="email"
                      id="email"
                      className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                        placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                      defaultValue={user.email}
                    />
                  </div>
                  <div>
                    <label htmlFor="company" className="block text-sm font-medium text-gray-300">
                      Company
                    </label>
                    <input
                      type="text"
                      name="company"
                      id="company"
                      className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                        placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                      defaultValue={user.company}
                    />
                  </div>
                  <div>
                    <label htmlFor="location" className="block text-sm font-medium text-gray-300">
                      Location
                    </label>
                    <input
                      type="text"
                      name="location"
                      id="location"
                      className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                        placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                        sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                      defaultValue={user.location}
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="bio" className="block text-sm font-medium text-gray-300">
                    Bio
                  </label>
                  <textarea
                    name="bio"
                    id="bio"
                    rows={4}
                    className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                      placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                      sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                    defaultValue={user.bio}
                  />
                </div>
                
                <div className="flex justify-end">
                  <Button variant="outline" className="mr-3">
                    Cancel
                  </Button>
                  <Button variant="glass" glow={true}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </Card>
          )}
          
          {activeTab === 'security' && (
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">Security Settings</h2>
              
              <div className="space-y-8">
                <div>
                  <h3 className="text-md font-medium text-white mb-4 flex items-center">
                    <KeyIcon className="h-5 w-5 mr-2 text-secondary-400" />
                    Change Password
                  </h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="current-password" className="block text-sm font-medium text-gray-300">
                        Current Password
                      </label>
                      <input
                        type="password"
                        name="current-password"
                        id="current-password"
                        className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                      />
                    </div>
                    <div className="sm:col-span-2 grid grid-cols-1 gap-6 sm:grid-cols-2">
                      <div>
                        <label htmlFor="new-password" className="block text-sm font-medium text-gray-300">
                          New Password
                        </label>
                        <input
                          type="password"
                          name="new-password"
                          id="new-password"
                          className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                            placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                            sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        />
                      </div>
                      <div>
                        <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-300">
                          Confirm New Password
                        </label>
                        <input
                          type="password"
                          name="confirm-password"
                          id="confirm-password"
                          className="mt-1 block w-full rounded-xl border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                            placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                            sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex justify-end">
                    <Button variant="glass">
                      Update Password
                    </Button>
                  </div>
                </div>
                
                <div className="pt-6 border-t border-white/10">
                  <h3 className="text-md font-medium text-white mb-4 flex items-center">
                    <DevicePhoneMobileIcon className="h-5 w-5 mr-2 text-secondary-400" />
                    Two-Factor Authentication
                  </h3>
                  <p className="text-sm text-gray-400 mb-4">
                    Add an extra layer of security to your account by enabling two-factor authentication.
                  </p>
                  <Button variant="outline">
                    Enable 2FA
                  </Button>
                </div>
              </div>
            </Card>
          )}
          
          {activeTab === 'notifications' && (
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-white mb-6">Notification Preferences</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-md font-medium text-white mb-4 flex items-center">
                    <EnvelopeIcon className="h-5 w-5 mr-2 text-secondary-400" />
                    Email Notifications
                  </h3>
                  <div className="space-y-4">
                    {['Security alerts', 'Account activity', 'Billing updates', 'Product updates', 'Newsletter'].map((item) => (
                      <div key={item} className="flex items-center">
                        <input
                          id={`email-${item.toLowerCase().replace(/\s+/g, '-')}`}
                          name={`email-${item.toLowerCase().replace(/\s+/g, '-')}`}
                          type="checkbox"
                          defaultChecked={item !== 'Newsletter'}
                          className="h-4 w-4 rounded border-gray-300 text-secondary-600 focus:ring-secondary-600"
                        />
                        <label
                          htmlFor={`email-${item.toLowerCase().replace(/\s+/g, '-')}`}
                          className="ml-3 block text-sm font-medium text-gray-300"
                        >
                          {item}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="pt-6 border-t border-white/10">
                  <h3 className="text-md font-medium text-white mb-4 flex items-center">
                    <BellIcon className="h-5 w-5 mr-2 text-secondary-400" />
                    Push Notifications
                  </h3>
                  <div className="space-y-4">
                    {['Deployment status', 'Server alerts', 'Domain expiration', 'Billing reminders'].map((item) => (
                      <div key={item} className="flex items-center">
                        <input
                          id={`push-${item.toLowerCase().replace(/\s+/g, '-')}`}
                          name={`push-${item.toLowerCase().replace(/\s+/g, '-')}`}
                          type="checkbox"
                          defaultChecked={item !== 'Billing reminders'}
                          className="h-4 w-4 rounded border-gray-300 text-secondary-600 focus:ring-secondary-600"
                        />
                        <label
                          htmlFor={`push-${item.toLowerCase().replace(/\s+/g, '-')}`}
                          className="ml-3 block text-sm font-medium text-gray-300"
                        >
                          {item}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button variant="glass">
                    Save Preferences
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
