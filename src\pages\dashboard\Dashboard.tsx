import { useState, useEffect } from 'react';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/20/solid';
import Card from '../../components/ui/Card';
import HostingStats from '../../components/dashboard/HostingStats';
import ActivityFeed from '../../components/dashboard/ActivityFeed';
import Button from '../../components/ui/Button';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler);

// Sample data for stats
const stats = [
  { name: 'CPU Usage', value: '45%', change: '12%', changeType: 'increase' },
  { name: 'Memory Usage', value: '1.2 GB', change: '8%', changeType: 'decrease' },
  { name: 'Storage', value: '8.2 GB', change: '5%', changeType: 'decrease' },
  { name: 'Bandwidth', value: '42 GB', change: '3%', changeType: 'increase' },
];

// Sample data for activity feed
const activities = [
  {
    id: '1',
    type: 'deployment',
    title: 'Deployment successful',
    description: 'Your application has been deployed successfully.',
    date: '3 hours ago',
  },
  {
    id: '2',
    type: 'domain',
    title: 'Domain renewed',
    description: 'Your domain myapp.io has been renewed for another year.',
    date: 'Yesterday',
  },
  {
    id: '3',
    type: 'ssl',
    title: 'SSL certificate issued',
    description: 'SSL certificate for test-site.dev has been issued.',
    date: '2 days ago',
  },
  {
    id: '4',
    type: 'storage',
    title: 'Storage limit reached',
    description: 'You are approaching your storage limit. Consider upgrading your plan.',
    date: '5 days ago',
  },
];

// Sample data for projects
const projects = [
  {
    id: '1',
    name: 'E-commerce Website',
    status: 'active',
    environment: 'production',
    url: 'https://myshop.com',
    lastDeployed: '2 hours ago',
  },
  {
    id: '2',
    name: 'Blog Platform',
    status: 'active',
    environment: 'staging',
    url: 'https://blog-staging.myapp.io',
    lastDeployed: '1 day ago',
  },
  {
    id: '3',
    name: 'Mobile API',
    status: 'active',
    environment: 'production',
    url: 'https://api.myapp.io',
    lastDeployed: '3 days ago',
  },
];

export default function Dashboard() {
  const [animatedData, setAnimatedData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
  
  // Real data for the chart
  const finalData = [65, 59, 80, 81, 56, 55, 40];
  
  // Animate the chart data on component mount
  useEffect(() => {
    const animationDuration = 1500; // ms
    const steps = 30;
    const stepDuration = animationDuration / steps;
    
    let currentStep = 0;
    
    const interval = setInterval(() => {
      if (currentStep >= steps) {
        clearInterval(interval);
        setAnimatedData(finalData);
        return;
      }
      
      const progress = (currentStep + 1) / steps;
      const newData = finalData.map((value) => Math.round(value * progress));
      
      setAnimatedData(newData);
      currentStep++;
    }, stepDuration);
    
    return () => clearInterval(interval);
  }, []);

  // Chart data with animated values
  const chartData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'Requests (thousands)',
        data: animatedData,
        fill: true,
        backgroundColor: 'rgba(139, 92, 246, 0.3)',
        borderColor: 'rgba(139, 92, 246, 1)',
        borderWidth: 2,
        tension: 0.4,
        pointBackgroundColor: 'rgba(139, 92, 246, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(139, 92, 246, 1)',
        pointRadius: 4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        titleFont: {
          family: 'Poppins',
          size: 13,
        },
        bodyFont: {
          family: 'Poppins',
          size: 12,
        },
        padding: 12,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
    },
    animation: {
      duration: 1000,
      easing: 'easeOutQuart',
    },
  };

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <HostingStats stats={stats} />
        </div>
        <div className="lg:col-span-1">
          <ActivityFeed activities={activities} />
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Weekly Requests</h3>
              <div className="flex items-center space-x-2">
                <Button variant="glass" size="sm">Export</Button>
              </div>
            </div>
            <div className="h-64 relative">
              <Line data={chartData} options={chartOptions} />
            </div>
          </Card>
        </div>
        <div className="lg:col-span-1">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Your Projects</h3>
              <Button variant="glass" size="sm">New Project</Button>
            </div>
            <div className="space-y-4">
              {projects.map((project) => (
                <div key={project.id} className="p-4 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="text-sm font-medium text-white">{project.name}</h4>
                      <p className="text-xs text-gray-400 mt-1">{project.environment} • {project.lastDeployed}</p>
                    </div>
                    <div className="flex items-center">
                      <span className="inline-flex h-2 w-2 rounded-full bg-green-400 mr-2"></span>
                      <span className="text-xs text-gray-300">{project.status}</span>
                    </div>
                  </div>
                  <div className="mt-3 flex items-center justify-between">
                    <a 
                      href={project.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-xs text-secondary-400 hover:text-secondary-300 transition-colors duration-200"
                    >
                      {project.url}
                    </a>
                    <Button variant="outline" size="sm">Manage</Button>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 text-center">
              <Button variant="ghost" size="sm">View All Projects</Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
