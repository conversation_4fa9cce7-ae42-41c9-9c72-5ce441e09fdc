import { useState, useEffect, Suspense } from 'react';
import { Outlet, useLocation, Link } from 'react-router-dom';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import {
  HomeIcon,
  UsersIcon,
  ServerIcon,
  CreditCardIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import LoadingScreen from '../ui/LoadingScreen';

const navigation = [
  { name: 'Dashboard', href: '/admin', icon: HomeIcon },
  { name: 'Users', href: '/admin/users', icon: UsersIcon },
  { name: 'Servers', href: '/admin/servers', icon: ServerIcon },
  { name: 'Billing', href: '/admin/billing', icon: CreditCardIcon },
  { name: 'Settings', href: '/admin/settings', icon: Cog6ToothIcon },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function AdminLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [pageTitle, setPageTitle] = useState('Admin Dashboard');
  const location = useLocation();

  // Update page title based on current route
  useEffect(() => {
    const path = location.pathname.split('/').pop() || 'admin';
    const formattedTitle = path === 'admin' ? 'Dashboard' : path.charAt(0).toUpperCase() + path.slice(1);
    setPageTitle(`Admin ${formattedTitle}`);
    document.title = `PoolotHost | Admin ${formattedTitle}`;
  }, [location]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Mobile sidebar */}
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setSidebarOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5 text-white hover:bg-white/10 rounded-md transition-colors duration-200"
                      onClick={() => setSidebarOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900/95 backdrop-blur-xl px-6 pb-4 border-r border-white/10">
                  <div className="flex h-16 shrink-0 items-center">
                    <Link to="/admin" className="flex items-center transition-transform duration-300 hover:scale-105">
                      <ShieldCheckIcon className="h-8 w-auto text-red-500" />
                      <span className="ml-2 text-xl font-bold text-white">Admin Panel</span>
                    </Link>
                  </div>
                  <nav className="flex flex-1 flex-col">
                    <ul role="list" className="flex flex-1 flex-col gap-y-7">
                      <li>
                        <ul role="list" className="-mx-2 space-y-1">
                          {navigation.map((item, index) => (
                            <li key={item.name} style={{ animationDelay: `${index * 50}ms` }} className="animate-fade-in">
                              <Link
                                to={item.href}
                                className={classNames(
                                  location.pathname === item.href
                                    ? 'bg-white/10 text-white border border-white/10'
                                    : 'text-gray-300 hover:text-white hover:bg-white/5',
                                  'group flex gap-x-3 rounded-xl p-2 text-sm leading-6 font-medium transition-all duration-200'
                                )}
                                onClick={() => setSidebarOpen(false)}
                              >
                                <item.icon
                                  className={classNames(
                                    location.pathname === item.href ? 'text-red-400' : 'text-gray-400 group-hover:text-red-400',
                                    'h-6 w-6 shrink-0 transition-colors duration-200'
                                  )}
                                  aria-hidden="true"
                                />
                                {item.name}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </li>
                      <li className="mt-auto">
                        <Link
                          to="/dashboard"
                          className="group -mx-2 flex gap-x-3 rounded-xl p-2 text-sm font-medium leading-6 text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-200"
                          onClick={() => setSidebarOpen(false)}
                        >
                          <ArrowRightOnRectangleIcon
                            className="h-6 w-6 shrink-0 text-gray-400 group-hover:text-red-400 transition-colors duration-200"
                            aria-hidden="true"
                          />
                          Exit Admin
                        </Link>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:w-72 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900/50 backdrop-blur-md border-r border-white/10 px-6 pb-4">
          <div className="flex h-16 shrink-0 items-center">
            <Link to="/admin" className="flex items-center transition-transform duration-300 hover:scale-105">
              <ShieldCheckIcon className="h-8 w-auto text-red-500" />
              <span className="ml-2 text-xl font-bold text-white">Admin Panel</span>
            </Link>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item, index) => (
                    <li key={item.name} style={{ animationDelay: `${index * 50}ms` }} className="animate-fade-in">
                      <Link
                        to={item.href}
                        className={classNames(
                          location.pathname === item.href
                            ? 'bg-white/10 text-white border border-white/10'
                            : 'text-gray-300 hover:text-white hover:bg-white/5',
                          'group flex gap-x-3 rounded-xl p-2 text-sm leading-6 font-medium transition-all duration-200 relative'
                        )}
                      >
                        <item.icon
                          className={classNames(
                            location.pathname === item.href ? 'text-red-400' : 'text-gray-400 group-hover:text-red-400',
                            'h-6 w-6 shrink-0 transition-colors duration-200'
                          )}
                          aria-hidden="true"
                        />
                        {item.name}
                        {location.pathname === item.href && (
                          <span className="absolute left-0 w-1 h-8 bg-red-500 rounded-r-full transform -translate-y-1"></span>
                        )}
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>
              <li className="mt-auto">
                <Link
                  to="/dashboard"
                  className="group -mx-2 flex gap-x-3 rounded-xl p-2 text-sm font-medium leading-6 text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-200"
                >
                  <ArrowRightOnRectangleIcon
                    className="h-6 w-6 shrink-0 text-gray-400 group-hover:text-red-400 transition-colors duration-200"
                    aria-hidden="true"
                  />
                  Exit Admin
                </Link>
              </li>
            </ul>
          </nav>

          {/* Decorative elements */}
          <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-red-900/20 to-transparent pointer-events-none"></div>
        </div>
      </div>

      <div className="lg:pl-72">
        <header className="sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-white/10 bg-gray-900/80 backdrop-blur-lg px-4 shadow-md sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-white lg:hidden hover:bg-white/10 rounded-md transition-colors duration-200"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>

          {/* Separator */}
          <div className="h-6 w-px bg-white/10 lg:hidden" aria-hidden="true" />

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-white">{pageTitle}</h1>
            </div>

            <div className="flex flex-1 items-center justify-end gap-x-4">
              <div className="flex items-center gap-x-4 lg:gap-x-6">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-red-600 flex items-center justify-center text-white">
                    <ShieldCheckIcon className="h-5 w-5" aria-hidden="true" />
                  </div>
                  <span className="ml-2 text-sm font-medium text-white hidden sm:block">Admin Mode</span>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="py-10 animate-fade-in">
          <div className="px-4 sm:px-6 lg:px-8">
            <Suspense fallback={
              <div className="flex items-center justify-center py-12">
                <div className="animate-pulse flex space-x-4 items-center">
                  <div className="h-12 w-12 rounded-full bg-red-500/20"></div>
                  <div className="space-y-2">
                    <div className="h-4 w-36 bg-red-500/20 rounded"></div>
                    <div className="h-4 w-24 bg-red-500/20 rounded"></div>
                  </div>
                </div>
              </div>
            }>
              <Outlet />
            </Suspense>
          </div>
        </main>

        {/* Decorative background elements */}
        <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-red-600/5 rounded-full blur-3xl"></div>
          <div className="absolute top-1/4 -left-40 w-80 h-80 bg-red-600/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 left-1/3 w-80 h-80 bg-red-600/5 rounded-full blur-3xl"></div>
        </div>
      </div>
    </div>
  );
}
