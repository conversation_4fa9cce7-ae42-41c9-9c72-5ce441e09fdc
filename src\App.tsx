import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import Home from './pages/Home';
import Login from './pages/Login';
import Signup from './pages/Signup';
import DashboardLayout from './components/dashboard/DashboardLayout';
import AdminLayout from './components/admin/AdminLayout';
import LoadingScreen from './components/ui/LoadingScreen';
import LoadingBar from './components/ui/LoadingBar';
import './App.css';

// Lazy load dashboard pages
const Dashboard = lazy(() => import('./pages/dashboard/Dashboard'));
const Services = lazy(() => import('./pages/dashboard/Services'));
const ServiceDetail = lazy(() => import('./pages/dashboard/ServiceDetail'));
const Hosting = lazy(() => import('./pages/dashboard/Hosting'));
const Domains = lazy(() => import('./pages/dashboard/Domains'));
const Billing = lazy(() => import('./pages/dashboard/Billing'));
const Analytics = lazy(() => import('./pages/dashboard/Analytics'));
const Settings = lazy(() => import('./pages/dashboard/Settings'));
const Profile = lazy(() => import('./pages/dashboard/Profile'));
const ProjectDetail = lazy(() => import('./pages/dashboard/ProjectDetail'));

// Lazy load project subpages
const ProjectDashboard = lazy(() => import('./pages/dashboard/project/ProjectDashboard'));
const ProjectLogs = lazy(() => import('./pages/dashboard/project/ProjectLogs'));
const ProjectEvents = lazy(() => import('./pages/dashboard/project/ProjectEvents'));
const ProjectEnvironment = lazy(() => import('./pages/dashboard/project/ProjectEnvironment'));
const ProjectSettings = lazy(() => import('./pages/dashboard/project/ProjectSettings'));
const ProjectShell = lazy(() => import('./pages/dashboard/project/ProjectShell'));
const ProjectConnections = lazy(() => import('./pages/dashboard/project/ProjectConnections'));
const ProjectMonitor = lazy(() => import('./pages/dashboard/project/ProjectMonitor'));
const ProjectMetrics = lazy(() => import('./pages/dashboard/project/ProjectMetrics'));
const ProjectManage = lazy(() => import('./pages/dashboard/project/ProjectManage'));
const ProjectScaling = lazy(() => import('./pages/dashboard/project/ProjectScaling'));
const ProjectPreviews = lazy(() => import('./pages/dashboard/project/ProjectPreviews'));
const ProjectDisks = lazy(() => import('./pages/dashboard/project/ProjectDisks'));
const ProjectJobs = lazy(() => import('./pages/dashboard/project/ProjectJobs'));

// Lazy load admin pages
const AdminDashboard = lazy(() => import('./pages/admin/Dashboard'));
const AdminUsers = lazy(() => import('./pages/admin/Users'));
const AdminServers = lazy(() => import('./pages/admin/Servers'));
const AdminBilling = lazy(() => import('./pages/admin/Billing'));
const AdminSettings = lazy(() => import('./pages/admin/Settings'));

// Simple auth check - in a real app, this would check JWT tokens, etc.
const isAuthenticated = () => {
  return true; // For demo purposes, always return true
};

// Simple admin check - in a real app, this would check user roles
const isAdmin = () => {
  return true; // For demo purposes, always return true
};

// Auth guard component
const ProtectedRoute = ({ children }: { children: JSX.Element }) => {
  if (!isAuthenticated()) {
    return <Navigate to="/login" replace />;
  }
  return children;
};

// Admin guard component
const AdminRoute = ({ children }: { children: JSX.Element }) => {
  if (!isAuthenticated() || !isAdmin()) {
    return <Navigate to="/dashboard" replace />;
  }
  return children;
};

function App() {
  return (
    <Router>
      <LoadingBar />
      <Suspense fallback={<LoadingScreen />}>
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />

          {/* User dashboard routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Dashboard />} />
            <Route path="services" element={<Services />} />
            <Route path="services/:id" element={<ServiceDetail />} />
            <Route path="hosting" element={<Hosting />} />
            <Route path="projects/:id" element={<ProjectDetail />}>
              <Route index element={<ProjectDashboard />} />
              <Route path="logs" element={<ProjectLogs />} />
              <Route path="events" element={<ProjectEvents />} />
              <Route path="settings" element={<ProjectSettings />} />
              <Route path="monitor" element={<ProjectMonitor />} />
              <Route path="metrics" element={<ProjectMetrics />} />
              <Route path="manage" element={<ProjectManage />} />
              <Route path="environment" element={<ProjectEnvironment />} />
              <Route path="shell" element={<ProjectShell />} />
              <Route path="scaling" element={<ProjectScaling />} />
              <Route path="previews" element={<ProjectPreviews />} />
              <Route path="disks" element={<ProjectDisks />} />
              <Route path="jobs" element={<ProjectJobs />} />
              <Route path="connections" element={<ProjectConnections />} />
            </Route>
            <Route path="domains" element={<Domains />} />
            <Route path="billing" element={<Billing />} />
            <Route path="analytics" element={<Analytics />} />
            <Route path="settings" element={<Settings />} />
            <Route path="profile" element={<Profile />} />
          </Route>

          {/* Admin dashboard routes */}
          <Route
            path="/admin"
            element={
              <AdminRoute>
                <AdminLayout />
              </AdminRoute>
            }
          >
            <Route index element={<AdminDashboard />} />
            <Route path="users" element={<AdminUsers />} />
            <Route path="servers" element={<AdminServers />} />
            <Route path="billing" element={<AdminBilling />} />
            <Route path="settings" element={<AdminSettings />} />
          </Route>

          {/* Fallback route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Suspense>
    </Router>
  );
}

export default App;
