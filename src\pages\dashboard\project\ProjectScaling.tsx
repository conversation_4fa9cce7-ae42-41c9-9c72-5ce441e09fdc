import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import {
  ArrowPathIcon,
  CpuChipIcon,
  CircleStackIcon,
  ServerIcon,
  ScaleIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  InformationCircleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

export default function ProjectScaling() {
  const project = useOutletContext<any>();
  const [selectedPlan, setSelectedPlan] = useState('standard');
  const [instances, setInstances] = useState(1);
  const [cpuAllocation, setCpuAllocation] = useState(1);
  const [memoryAllocation, setMemoryAllocation] = useState(1);
  const [autoScaling, setAutoScaling] = useState(false);
  const [minInstances, setMinInstances] = useState(1);
  const [maxInstances, setMaxInstances] = useState(3);
  const [cpuThreshold, setCpuThreshold] = useState(80);

  // Plans data
  const plans = [
    {
      id: 'free',
      name: 'Free',
      cpu: '0.1 CPU',
      memory: '512 MB',
      price: '$0/month',
      description: 'For personal projects and experiments',
      disk: '1 GB',
    },
    {
      id: 'starter',
      name: 'Starter',
      cpu: '0.5 CPU',
      memory: '512 MB',
      price: '$3.99/month',
      description: 'For small personal projects',
      disk: '2 GB',
    },
    {
      id: 'tiny',
      name: 'Tiny',
      cpu: '1 CPU',
      memory: '1 GB',
      price: '$5.99/month',
      description: 'For small production applications',
      disk: '5 GB',
    },
    {
      id: 'standard',
      name: 'Standard',
      cpu: '1 CPU',
      memory: '2 GB',
      price: '$9.99/month',
      description: 'For medium-sized applications',
      disk: '10 GB',
    },
    {
      id: 'pro',
      name: 'Pro',
      cpu: '2 CPU',
      memory: '4 GB',
      price: '$19.99/month',
      description: 'For production applications with higher traffic',
      disk: '20 GB',
    },
    {
      id: 'pro-plus',
      name: 'Pro Plus',
      cpu: '4 CPU',
      memory: '8 GB',
      price: '$34.99/month',
      description: 'For high-traffic applications',
      disk: '40 GB',
    },
    {
      id: 'pro-max',
      name: 'Pro Max',
      cpu: '4 CPU',
      memory: '16 GB',
      price: '$64.99/month',
      description: 'For resource-intensive applications',
      disk: '80 GB',
    },
    {
      id: 'pro-ultra',
      name: 'Pro Ultra',
      cpu: '8 CPU',
      memory: '32 GB',
      price: '$109.99/month',
      description: 'For enterprise applications with high demands',
      disk: '160 GB',
    },
    {
      id: 'custom',
      name: 'Custom',
      cpu: '16-64 CPU',
      memory: '64-512 GB',
      price: 'Contact us',
      description: 'For large-scale enterprise applications',
      disk: '500+ GB',
    },
  ];

  // Function to handle scaling changes
  const handleScalingChange = () => {
    // In a real app, this would call an API to update scaling settings
    console.log('Updating scaling settings...');
    // For demo purposes, we'll just show a success message
    alert('Scaling settings updated successfully!');
  };

  // Function to increment/decrement instances
  const adjustInstances = (amount: number) => {
    const newValue = instances + amount;
    if (newValue >= 1 && newValue <= 10) {
      setInstances(newValue);
    }
  };

  // Function to increment/decrement CPU allocation
  const adjustCpu = (amount: number) => {
    const newValue = cpuAllocation + amount;
    if (newValue >= 0.5 && newValue <= 8) {
      setCpuAllocation(newValue);
    }
  };

  // Function to increment/decrement memory allocation
  const adjustMemory = (amount: number) => {
    const newValue = memoryAllocation + amount;
    if (newValue >= 0.5 && newValue <= 16) {
      setMemoryAllocation(newValue);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-white">Scaling & Resources</h2>
          <p className="text-sm text-gray-400">Configure your application's computing resources</p>
        </div>
        <Button
          variant="glass"
          size="sm"
          icon={<ArrowPathIcon className="h-4 w-4" />}
          onClick={handleScalingChange}
        >
          Apply Changes
        </Button>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Resource Plans</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`p-4 rounded-xl border ${
                selectedPlan === plan.id
                  ? 'border-secondary-500 bg-secondary-900/20'
                  : 'border-white/10 bg-white/5 hover:bg-white/10'
              } transition-all duration-200 cursor-pointer`}
              onClick={() => setSelectedPlan(plan.id)}
            >
              <div className="flex justify-between items-start mb-3">
                <h4 className="text-base font-medium text-white">{plan.name}</h4>
                {selectedPlan === plan.id && (
                  <CheckCircleIcon className="h-5 w-5 text-secondary-500" />
                )}
              </div>
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-300">
                  <CpuChipIcon className="h-4 w-4 mr-2 text-gray-400" />
                  {plan.cpu}
                </div>
                <div className="flex items-center text-sm text-gray-300">
                  <CircleStackIcon className="h-4 w-4 mr-2 text-gray-400" />
                  {plan.memory}
                </div>
                <div className="flex items-center text-sm text-gray-300">
                  <ServerIcon className="h-4 w-4 mr-2 text-gray-400" />
                  {plan.disk}
                </div>
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-semibold text-white">{plan.price}</span>
                <span className="text-xs text-gray-400">{plan.description}</span>
                {plan.id === 'custom' && (
                  <Button
                    variant="glass"
                    size="sm"
                    className="mt-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      alert('Contact support for custom plan options');
                    }}
                  >
                    Contact Support
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 rounded-lg bg-secondary-900/50 border border-secondary-500/30">
          <div className="flex items-start">
            <InformationCircleIcon className="h-5 w-5 text-secondary-400 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <p className="text-sm text-gray-300">
                Changing plans will affect your billing immediately. Your application may experience a brief downtime during the transition.
              </p>
            </div>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Manual Scaling</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 rounded-lg bg-white/5 border border-white/10">
            <h4 className="text-sm font-medium text-gray-400 mb-2">Instances</h4>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ServerIcon className="h-5 w-5 text-secondary-400 mr-2" />
                <span className="text-xl font-semibold text-white">{instances}</span>
              </div>
              <div className="flex space-x-2">
                <button
                  className="p-1 rounded-md bg-white/10 text-white hover:bg-white/20 transition-colors duration-200"
                  onClick={() => adjustInstances(-1)}
                  disabled={instances <= 1}
                >
                  <ChevronDownIcon className="h-5 w-5" />
                </button>
                <button
                  className="p-1 rounded-md bg-white/10 text-white hover:bg-white/20 transition-colors duration-200"
                  onClick={() => adjustInstances(1)}
                  disabled={instances >= 10}
                >
                  <ChevronUpIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
            <p className="text-xs text-gray-400 mt-2">Number of application instances</p>
          </div>

          <div className="p-4 rounded-lg bg-white/5 border border-white/10">
            <h4 className="text-sm font-medium text-gray-400 mb-2">CPU</h4>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CpuChipIcon className="h-5 w-5 text-primary-400 mr-2" />
                <span className="text-xl font-semibold text-white">{cpuAllocation} vCPU</span>
              </div>
              <div className="flex space-x-2">
                <button
                  className="p-1 rounded-md bg-white/10 text-white hover:bg-white/20 transition-colors duration-200"
                  onClick={() => adjustCpu(-0.5)}
                  disabled={cpuAllocation <= 0.5}
                >
                  <ChevronDownIcon className="h-5 w-5" />
                </button>
                <button
                  className="p-1 rounded-md bg-white/10 text-white hover:bg-white/20 transition-colors duration-200"
                  onClick={() => adjustCpu(0.5)}
                  disabled={cpuAllocation >= 8}
                >
                  <ChevronUpIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
            <p className="text-xs text-gray-400 mt-2">CPU allocation per instance</p>
          </div>

          <div className="p-4 rounded-lg bg-white/5 border border-white/10">
            <h4 className="text-sm font-medium text-gray-400 mb-2">Memory</h4>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CircleStackIcon className="h-5 w-5 text-green-400 mr-2" />
                <span className="text-xl font-semibold text-white">{memoryAllocation} GB</span>
              </div>
              <div className="flex space-x-2">
                <button
                  className="p-1 rounded-md bg-white/10 text-white hover:bg-white/20 transition-colors duration-200"
                  onClick={() => adjustMemory(-0.5)}
                  disabled={memoryAllocation <= 0.5}
                >
                  <ChevronDownIcon className="h-5 w-5" />
                </button>
                <button
                  className="p-1 rounded-md bg-white/10 text-white hover:bg-white/20 transition-colors duration-200"
                  onClick={() => adjustMemory(0.5)}
                  disabled={memoryAllocation >= 16}
                >
                  <ChevronUpIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
            <p className="text-xs text-gray-400 mt-2">Memory allocation per instance</p>
          </div>
        </div>

        <div className="mt-4 p-4 rounded-lg bg-white/5 border border-white/10">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <h4 className="text-sm font-medium text-white">Estimated Monthly Cost</h4>
            </div>
            <span className="text-lg font-semibold text-white">${(15 * instances * cpuAllocation * (memoryAllocation / 2)).toFixed(2)}</span>
          </div>
          <p className="text-xs text-gray-400">Based on your current configuration</p>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Auto Scaling</h3>
          <div className="relative inline-block w-12 mr-2 align-middle select-none">
            <input
              type="checkbox"
              name="toggle"
              id="toggle"
              className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 border-gray-300 appearance-none cursor-pointer transition-transform duration-200 ease-in-out"
              checked={autoScaling}
              onChange={() => setAutoScaling(!autoScaling)}
              style={{
                right: autoScaling ? '0' : 'auto',
                transform: autoScaling ? 'translateX(100%)' : 'none',
                backgroundColor: autoScaling ? 'rgb(139, 92, 246)' : 'white',
              }}
            />
            <label
              htmlFor="toggle"
              className="toggle-label block overflow-hidden h-6 rounded-full bg-gray-700 cursor-pointer"
            ></label>
          </div>
        </div>

        {autoScaling && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Minimum Instances
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10
                    placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                    sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                  value={minInstances}
                  onChange={(e) => setMinInstances(parseInt(e.target.value))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Maximum Instances
                </label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10
                    placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                    sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                  value={maxInstances}
                  onChange={(e) => setMaxInstances(parseInt(e.target.value))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  CPU Threshold (%)
                </label>
                <input
                  type="number"
                  min="50"
                  max="95"
                  className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10
                    placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                    sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                  value={cpuThreshold}
                  onChange={(e) => setCpuThreshold(parseInt(e.target.value))}
                />
              </div>
            </div>

            <div className="p-4 rounded-lg bg-secondary-900/50 border border-secondary-500/30">
              <div className="flex items-start">
                <InformationCircleIcon className="h-5 w-5 text-secondary-400 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm text-gray-300">
                    Auto scaling will automatically adjust the number of instances based on CPU usage. New instances will be added when CPU usage exceeds the threshold, and removed when usage drops significantly.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
