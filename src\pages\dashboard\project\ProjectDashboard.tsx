import { useOutletContext, Link, useParams } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import {
  ArrowPathIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  CpuChipIcon,
  ServerIcon,
  CircleStackIcon,
  CommandLineIcon,
  KeyIcon,
  DocumentTextIcon,
  LinkIcon,
} from '@heroicons/react/24/outline';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

export default function ProjectDashboard() {
  const project = useOutletContext<any>();
  const { id } = useParams<{ id: string }>();

  // If project data is not available, show a loading state
  if (!project) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-white/10 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="p-4 rounded-lg bg-white/5 border border-white/10">
                <div className="h-4 bg-white/10 rounded w-1/2 mb-3"></div>
                <div className="h-6 bg-white/10 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <CheckCircleIcon className="mr-1 h-3 w-3 text-green-400" />
            Success
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
            <XCircleIcon className="h-3 w-3 mr-1 text-red-400" />
            Failed
          </span>
        );
      default:
        return null;
    }
  };

  // CPU usage chart data
  const cpuChartData = {
    labels: ['6h ago', '5h ago', '4h ago', '3h ago', '2h ago', '1h ago', 'Now'],
    datasets: [
      {
        label: 'CPU Usage (%)',
        data: project?.metrics?.cpu || [45, 42, 47, 50, 48, 46, 44],
        fill: true,
        backgroundColor: 'rgba(139, 92, 246, 0.2)',
        borderColor: 'rgba(139, 92, 246, 1)',
        borderWidth: 2,
        tension: 0.4,
        pointBackgroundColor: 'rgba(139, 92, 246, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(139, 92, 246, 1)',
        pointRadius: 4,
      },
    ],
  };

  // Memory usage chart data
  const memoryChartData = {
    labels: ['6h ago', '5h ago', '4h ago', '3h ago', '2h ago', '1h ago', 'Now'],
    datasets: [
      {
        label: 'Memory Usage (MB)',
        data: project?.metrics?.memory || [512, 520, 510, 530, 525, 515, 518],
        fill: true,
        backgroundColor: 'rgba(14, 165, 233, 0.2)',
        borderColor: 'rgba(14, 165, 233, 1)',
        borderWidth: 2,
        tension: 0.4,
        pointBackgroundColor: 'rgba(14, 165, 233, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(14, 165, 233, 1)',
        pointRadius: 4,
      },
    ],
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        titleFont: {
          family: 'Poppins',
          size: 13,
        },
        bodyFont: {
          family: 'Poppins',
          size: 12,
        },
        padding: 12,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
    },
    animation: {
      duration: 1000,
      easing: 'easeOutQuart',
    },
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-white mb-4">Project Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="p-4 rounded-lg bg-white/5 border border-white/10">
            <h3 className="text-sm font-medium text-gray-400 mb-1">Status</h3>
            <div className="flex items-center">
              <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
                <span className="h-1.5 w-1.5 rounded-full bg-green-400 mr-1.5 animate-pulse"></span>
                {project?.status === 'running' ? 'Running' : 'Stopped'}
              </span>
              <span className="ml-2 text-white">{project?.environment || 'Production'}</span>
            </div>
          </div>
          <div className="p-4 rounded-lg bg-white/5 border border-white/10">
            <h3 className="text-sm font-medium text-gray-400 mb-1">Framework</h3>
            <div className="text-white">{project?.framework}</div>
          </div>
          <div className="p-4 rounded-lg bg-white/5 border border-white/10">
            <h3 className="text-sm font-medium text-gray-400 mb-1">Last Deployed</h3>
            <div className="text-white">{project?.lastDeployed}</div>
          </div>
          <div className="p-4 rounded-lg bg-white/5 border border-white/10">
            <h3 className="text-sm font-medium text-gray-400 mb-1">URL</h3>
            <a
              href={project?.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-secondary-400 hover:text-secondary-300 transition-colors duration-200 truncate block"
            >
              {project?.url}
            </a>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold leading-6 text-white">CPU Usage</h3>
            <Button
              variant="outline"
              size="sm"
              icon={<ArrowPathIcon className="h-4 w-4" />}
            >
              Refresh
            </Button>
          </div>
          <div className="h-64 relative">
            <Line data={cpuChartData} options={chartOptions} />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold leading-6 text-white">Memory Usage</h3>
            <Button
              variant="outline"
              size="sm"
              icon={<ArrowPathIcon className="h-4 w-4" />}
            >
              Refresh
            </Button>
          </div>
          <div className="h-64 relative">
            <Line data={memoryChartData} options={chartOptions} />
          </div>
        </Card>
      </div>

      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-base font-semibold leading-6 text-white">Recent Deployments</h3>
          <Button
            variant="outline"
            size="sm"
          >
            View All
          </Button>
        </div>
        <div className="space-y-4">
          {project?.deployments?.length > 0 ? project.deployments.slice(0, 3).map((deployment: any) => (
            <div
              key={deployment.id}
              className="p-4 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-3">
                    {getStatusBadge(deployment.status)}
                  </div>
                  <div>
                    <div className="flex items-center">
                      <p className="text-sm text-white font-mono">{deployment.commit.substring(0, 7)}</p>
                      <p className="ml-2 text-sm text-gray-400">{deployment.message}</p>
                    </div>
                    <div className="flex items-center mt-1">
                      <p className="text-xs text-gray-500">{deployment.author}</p>
                      <span className="mx-1 text-gray-600">•</span>
                      <p className="text-xs text-gray-500">{deployment.timestamp}</p>
                      <span className="mx-1 text-gray-600">•</span>
                      <p className="text-xs text-gray-500">{deployment.duration}</p>
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                >
                  View
                </Button>
              </div>
            </div>
          )) : (
            <div className="p-4 rounded-lg bg-white/5 border border-white/10 text-center">
              <p className="text-gray-400">No deployments found</p>
            </div>
          )}
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold leading-6 text-white">Recent Events</h3>
            <Button
              variant="outline"
              size="sm"
            >
              View All
            </Button>
          </div>
          <div className="space-y-4">
            {project?.events?.length > 0 ? project.events.slice(0, 3).map((event: any) => (
              <div
                key={event.id}
                className="p-3 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="mr-3">
                      {getStatusBadge(event.status)}
                    </div>
                    <div>
                      <p className="text-sm text-white">{event.message}</p>
                      <p className="text-xs text-gray-400">{event.timestamp}</p>
                    </div>
                  </div>
                </div>
              </div>
            )) : (
              <div className="p-3 rounded-lg bg-white/5 border border-white/10 text-center">
                <p className="text-gray-400">No events found</p>
              </div>
            )}
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold leading-6 text-white">Quick Actions</h3>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Button
              variant="outline"
              fullWidth
              className="justify-start"
              icon={<CommandLineIcon className="h-5 w-5" />}
              as={Link}
              to={`/dashboard/projects/${id}/shell`}
            >
              Shell
            </Button>
            <Button
              variant="outline"
              fullWidth
              className="justify-start"
              icon={<KeyIcon className="h-5 w-5" />}
              as={Link}
              to={`/dashboard/projects/${id}/environment`}
            >
              Environment
            </Button>
            <Button
              variant="outline"
              fullWidth
              className="justify-start"
              icon={<DocumentTextIcon className="h-5 w-5" />}
              as={Link}
              to={`/dashboard/projects/${id}/logs`}
            >
              Logs
            </Button>
            <Button
              variant="outline"
              fullWidth
              className="justify-start"
              icon={<ArrowPathIcon className="h-5 w-5" />}
            >
              Redeploy
            </Button>
            <Button
              variant="outline"
              fullWidth
              className="justify-start"
              icon={<LinkIcon className="h-5 w-5" />}
              as={Link}
              to={`/dashboard/projects/${id}/connections`}
            >
              Connections
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}
