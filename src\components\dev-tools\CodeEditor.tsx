import React, { useState, useRef, useCallback } from 'react';
import Editor from '@monaco-editor/react';
import { 
  DocumentIcon,
  CloudArrowDownIcon,
  PlayIcon,
  StopIcon,
  Cog6ToothIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { useHotkeys } from 'react-hotkeys-hook';

interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  content?: string;
  language?: string;
}

interface CodeEditorProps {
  file: FileNode | null;
  onSave: (file: FileNode, content: string) => void;
  onRun?: (file: FileNode) => void;
}

const CodeEditor: React.FC<CodeEditorProps> = ({ file, onSave, onRun }) => {
  const [content, setContent] = useState(file?.content || '');
  const [isModified, setIsModified] = useState(false);
  const [fontSize, setFontSize] = useState(14);
  const [theme, setTheme] = useState<'vs-dark' | 'light'>('vs-dark');
  const [isRunning, setIsRunning] = useState(false);
  const editorRef = useRef<any>(null);

  // Keyboard shortcuts
  useHotkeys('ctrl+s', (e) => {
    e.preventDefault();
    handleSave();
  }, { enableOnFormTags: true });

  useHotkeys('ctrl+r', (e) => {
    e.preventDefault();
    handleRun();
  }, { enableOnFormTags: true });

  useHotkeys('ctrl+f', (e) => {
    e.preventDefault();
    if (editorRef.current) {
      editorRef.current.trigger('', 'actions.find');
    }
  }, { enableOnFormTags: true });

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
    
    // Configure editor options
    editor.updateOptions({
      fontSize,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      wordWrap: 'on',
      lineNumbers: 'on',
      renderWhitespace: 'selection',
      bracketPairColorization: { enabled: true }
    });
  };

  const handleContentChange = useCallback((value: string | undefined) => {
    const newContent = value || '';
    setContent(newContent);
    setIsModified(newContent !== (file?.content || ''));
  }, [file?.content]);

  const handleSave = useCallback(() => {
    if (file && isModified) {
      onSave(file, content);
      setIsModified(false);
    }
  }, [file, content, isModified, onSave]);

  const handleRun = useCallback(async () => {
    if (!file || !onRun) return;
    
    setIsRunning(true);
    try {
      await onRun(file);
    } finally {
      setIsRunning(false);
    }
  }, [file, onRun]);

  const getLanguageFromFilename = (filename: string): string => {
    const extension = filename.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'json': 'json',
      'md': 'markdown',
      'py': 'python',
      'php': 'php',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'go': 'go',
      'rs': 'rust',
      'rb': 'ruby',
      'yml': 'yaml',
      'yaml': 'yaml',
      'xml': 'xml',
      'sql': 'sql',
      'sh': 'shell',
      'bash': 'shell',
      'dockerfile': 'dockerfile'
    };
    return languageMap[extension || ''] || 'plaintext';
  };

  const downloadFile = () => {
    if (!file) return;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const formatDocument = () => {
    if (editorRef.current) {
      editorRef.current.trigger('', 'editor.action.formatDocument');
    }
  };

  if (!file) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <DocumentIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">No File Selected</h3>
          <p className="text-sm">Select a file from the file manager to start editing</p>
          <div className="mt-4 text-xs space-y-1">
            <p><kbd className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded">Ctrl+S</kbd> Save file</p>
            <p><kbd className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded">Ctrl+R</kbd> Run file</p>
            <p><kbd className="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded">Ctrl+F</kbd> Find in file</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-800">
      {/* Editor Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center space-x-3">
          <DocumentIcon className="h-5 w-5 text-gray-500" />
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {file.name}
            {isModified && <span className="text-orange-500 ml-1">●</span>}
          </span>
          <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
            {getLanguageFromFilename(file.name)}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Font Size Controls */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setFontSize(Math.max(10, fontSize - 1))}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xs"
            >
              A-
            </button>
            <span className="text-xs text-gray-500 w-8 text-center">{fontSize}</span>
            <button
              onClick={() => setFontSize(Math.min(24, fontSize + 1))}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xs"
            >
              A+
            </button>
          </div>

          {/* Theme Toggle */}
          <button
            onClick={() => setTheme(theme === 'vs-dark' ? 'light' : 'vs-dark')}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            title="Toggle Theme"
          >
            <Cog6ToothIcon className="h-4 w-4" />
          </button>

          {/* Format Document */}
          <button
            onClick={formatDocument}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            title="Format Document (Alt+Shift+F)"
          >
            <ArrowPathIcon className="h-4 w-4" />
          </button>

          {/* Find */}
          <button
            onClick={() => editorRef.current?.trigger('', 'actions.find')}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            title="Find (Ctrl+F)"
          >
            <MagnifyingGlassIcon className="h-4 w-4" />
          </button>

          {/* Download */}
          <button
            onClick={downloadFile}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            title="Download File"
          >
            <CloudArrowDownIcon className="h-4 w-4" />
          </button>

          {/* Run Button */}
          {onRun && (
            <button
              onClick={handleRun}
              disabled={isRunning}
              className={`flex items-center space-x-1 px-3 py-1 rounded text-sm ${
                isRunning
                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  : 'bg-green-500 text-white hover:bg-green-600'
              }`}
              title="Run File (Ctrl+R)"
            >
              {isRunning ? (
                <StopIcon className="h-4 w-4" />
              ) : (
                <PlayIcon className="h-4 w-4" />
              )}
              <span>{isRunning ? 'Running...' : 'Run'}</span>
            </button>
          )}

          {/* Save Button */}
          <button
            onClick={handleSave}
            disabled={!isModified}
            className={`px-3 py-1 rounded text-sm ${
              isModified
                ? 'bg-blue-500 text-white hover:bg-blue-600'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
            title="Save File (Ctrl+S)"
          >
            Save
          </button>
        </div>
      </div>

      {/* Monaco Editor */}
      <div className="flex-1">
        <Editor
          height="100%"
          language={getLanguageFromFilename(file.name)}
          value={content}
          theme={theme}
          onChange={handleContentChange}
          onMount={handleEditorDidMount}
          options={{
            fontSize,
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            wordWrap: 'on',
            lineNumbers: 'on',
            renderWhitespace: 'selection',
            bracketPairColorization: { enabled: true },
            suggestOnTriggerCharacters: true,
            quickSuggestions: true,
            parameterHints: { enabled: true },
            formatOnPaste: true,
            formatOnType: true
          }}
        />
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-3 py-1 bg-gray-100 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 text-xs text-gray-600 dark:text-gray-300">
        <div className="flex items-center space-x-4">
          <span>Lines: {content.split('\n').length}</span>
          <span>Characters: {content.length}</span>
          <span>Language: {getLanguageFromFilename(file.name)}</span>
        </div>
        <div className="flex items-center space-x-4">
          <span>UTF-8</span>
          <span>LF</span>
          {isModified && <span className="text-orange-500">● Unsaved changes</span>}
        </div>
      </div>
    </div>
  );
};

export default CodeEditor;
