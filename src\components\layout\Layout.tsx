import { ReactNode, useEffect, useState } from 'react';
import Navbar from './Navbar';
import Footer from './Footer';

interface LayoutProps {
  children: ReactNode;
  fullWidth?: boolean;
}

export default function Layout({ children, fullWidth = false }: LayoutProps) {
  const [mounted, setMounted] = useState(false);

  // Add animation effect when component mounts
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className={`flex flex-col min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 ${mounted ? 'opacity-100' : 'opacity-0'} transition-opacity duration-500`}>
      {/* Fixed navbar with padding to prevent content from being hidden */}
      <Navbar />
      <div className="pt-16 md:pt-20"> {/* Padding for fixed navbar */}
        <main className={`flex-grow ${fullWidth ? '' : 'container mx-auto px-4 sm:px-6 lg:px-8'}`}>
          {children}
        </main>
      </div>
      <Footer />

      {/* Decorative background elements */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-secondary-600/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/4 -left-40 w-80 h-80 bg-primary-600/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 left-1/3 w-80 h-80 bg-secondary-600/5 rounded-full blur-3xl"></div>
      </div>
    </div>
  );
}
