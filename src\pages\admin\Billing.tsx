import { useState } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  CreditCardIcon,
  CurrencyDollarIcon,
  UserIcon,
  DocumentTextIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CheckIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

// Sample data for revenue stats
const revenueStats = [
  { name: 'Monthly Revenue', value: '$48,290', change: '12%', changeType: 'increase' },
  { name: 'Annual Revenue', value: '$578,480', change: '8%', changeType: 'increase' },
  { name: 'Active Subscriptions', value: '1,284', change: '5%', changeType: 'increase' },
  { name: 'Avg. Customer Value', value: '$37.60', change: '3%', changeType: 'increase' },
];

// Sample data for invoices
const invoices = [
  {
    id: 'INV-001',
    customer: 'Jane <PERSON>',
    email: '<EMAIL>',
    amount: '$19.99',
    status: 'paid',
    date: 'Jul 1, 2023',
    plan: 'Pro',
  },
  {
    id: 'INV-002',
    customer: 'Michael Foster',
    email: '<EMAIL>',
    amount: '$3.99',
    status: 'paid',
    date: 'Jul 1, 2023',
    plan: 'Starter',
  },
  {
    id: 'INV-003',
    customer: 'Dries Vincent',
    email: '<EMAIL>',
    amount: '$109.99',
    status: 'paid',
    date: 'Jul 1, 2023',
    plan: 'Pro Ultra',
  },
  {
    id: 'INV-004',
    customer: 'Lindsay Walton',
    email: '<EMAIL>',
    amount: '$9.99',
    status: 'pending',
    date: 'Jul 1, 2023',
    plan: 'Standard',
  },
  {
    id: 'INV-005',
    customer: 'Courtney Henry',
    email: '<EMAIL>',
    amount: '$5.99',
    status: 'paid',
    date: 'Jul 1, 2023',
    plan: 'Tiny',
  },
  {
    id: 'INV-006',
    customer: 'Tom Cook',
    email: '<EMAIL>',
    amount: '$64.99',
    status: 'failed',
    date: 'Jul 1, 2023',
    plan: 'Pro Max',
  },
  {
    id: 'INV-007',
    customer: 'Whitney Francis',
    email: '<EMAIL>',
    amount: '$34.99',
    status: 'paid',
    date: 'Jul 1, 2023',
    plan: 'Pro Plus',
  },
];

// Sample data for revenue by plan
const revenueByPlan = {
  labels: ['Free', 'Starter', 'Tiny', 'Standard', 'Pro'],
  datasets: [
    {
      label: 'Revenue',
      data: [0, 15960, 17970, 39960, 59970],
      backgroundColor: [
        'rgba(75, 192, 192, 0.7)',
        'rgba(14, 165, 233, 0.7)',
        'rgba(139, 92, 246, 0.7)',
        'rgba(220, 38, 38, 0.7)',
        'rgba(245, 158, 11, 0.7)',
      ],
      borderColor: [
        'rgba(75, 192, 192, 1)',
        'rgba(14, 165, 233, 1)',
        'rgba(139, 92, 246, 1)',
        'rgba(220, 38, 38, 1)',
        'rgba(245, 158, 11, 1)',
      ],
      borderWidth: 1,
      borderRadius: 4,
    },
  ],
};

// Chart options
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
    tooltip: {
      backgroundColor: 'rgba(17, 24, 39, 0.8)',
      titleFont: {
        family: 'Poppins',
        size: 13,
      },
      bodyFont: {
        family: 'Poppins',
        size: 12,
      },
      padding: 12,
      borderColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        color: 'rgba(255, 255, 255, 0.05)',
      },
      ticks: {
        color: 'rgba(255, 255, 255, 0.6)',
        font: {
          family: 'Poppins',
        },
        callback: function(value: any) {
          return '$' + value.toLocaleString();
        }
      },
    },
    x: {
      grid: {
        color: 'rgba(255, 255, 255, 0.05)',
      },
      ticks: {
        color: 'rgba(255, 255, 255, 0.6)',
        font: {
          family: 'Poppins',
        },
      },
    },
  },
  animation: {
    duration: 1000,
    easing: 'easeOutQuart',
  },
};

export default function AdminBilling() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [invoicesPerPage] = useState(5);

  // Filter invoices based on search term and status
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch =
      invoice.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Pagination
  const indexOfLastInvoice = currentPage * invoicesPerPage;
  const indexOfFirstInvoice = indexOfLastInvoice - invoicesPerPage;
  const currentInvoices = filteredInvoices.slice(indexOfFirstInvoice, indexOfLastInvoice);
  const totalPages = Math.ceil(filteredInvoices.length / invoicesPerPage);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <CheckIcon className="mr-1 h-3 w-3 text-green-400" />
            Paid
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-900/30 border border-yellow-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-yellow-300">
            <ExclamationTriangleIcon className="mr-1 h-3 w-3 text-yellow-400" />
            Pending
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
            <XMarkIcon className="mr-1 h-3 w-3 text-red-400" />
            Failed
          </span>
        );
      default:
        return null;
    }
  };

  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ');
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Billing Management</h1>
          <p className="mt-1 text-sm text-gray-400">Monitor revenue, subscriptions, and payment processing</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="glass"
            size="sm"
            icon={<DocumentTextIcon className="h-5 w-5" />}
          >
            Export Report
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {revenueStats.map((stat, index) => (
          <Card
            key={stat.name}
            className="p-6 relative overflow-hidden"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 rounded-full bg-red-900/30 border border-red-500/30 flex items-center justify-center">
                  <CurrencyDollarIcon className="h-6 w-6 text-red-400" aria-hidden="true" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-300 truncate">{stat.name}</dt>
                  <dd>
                    <div className="text-lg font-semibold text-white">{stat.value}</div>
                  </dd>
                </dl>
              </div>
            </div>
            <div className="absolute bottom-0 inset-x-0 bg-gradient-to-r from-red-500/0 via-red-500/10 to-red-500/0 h-0.5"></div>

            <div className="absolute top-6 right-6">
              <div
                className={classNames(
                  stat.changeType === 'increase' ? 'bg-green-900/50 text-green-300 border border-green-500/30' : 'bg-red-900/50 text-red-300 border border-red-500/30',
                  'inline-flex items-baseline rounded-full px-2.5 py-0.5 text-sm font-medium backdrop-blur-sm'
                )}
              >
                {stat.changeType === 'increase' ? (
                  <ArrowUpIcon
                    className="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center text-green-400"
                    aria-hidden="true"
                  />
                ) : (
                  <ArrowDownIcon
                    className="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center text-red-400"
                    aria-hidden="true"
                  />
                )}
                {stat.change}
              </div>
            </div>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Revenue by Plan</h3>
              <Button variant="outline" size="sm">Details</Button>
            </div>
            <div className="h-80 relative">
              <Bar data={revenueByPlan} options={chartOptions} />
            </div>
            <div className="mt-6 grid grid-cols-5 gap-2">
              <div className="text-center">
                <div className="text-xs text-gray-400">Free</div>
                <div className="text-lg font-semibold text-white">$0</div>
                <div className="text-xs text-gray-500">0%</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-400">Starter</div>
                <div className="text-lg font-semibold text-white">$15,960</div>
                <div className="text-xs text-gray-500">12%</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-400">Tiny</div>
                <div className="text-lg font-semibold text-white">$17,970</div>
                <div className="text-xs text-gray-500">13%</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-400">Standard</div>
                <div className="text-lg font-semibold text-white">$39,960</div>
                <div className="text-xs text-gray-500">30%</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-400">Pro</div>
                <div className="text-lg font-semibold text-white">$59,970</div>
                <div className="text-xs text-gray-500">45%</div>
              </div>
            </div>
          </Card>
        </div>

        <div className="lg:col-span-2">
          <Card className="p-6">
            <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
              <div className="flex items-center">
                <h3 className="text-base font-semibold leading-6 text-white">Recent Invoices</h3>
              </div>
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
                <div className="relative flex-grow max-w-xs">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                  </div>
                  <input
                    type="text"
                    className="block w-full rounded-xl border-0 py-1.5 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10
                      placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500
                      sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                    placeholder="Search invoices"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <select
                  className="rounded-xl border-0 py-1.5 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10
                    focus:ring-2 focus:ring-inset focus:ring-red-500
                    sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="all">All Status</option>
                  <option value="paid">Paid</option>
                  <option value="pending">Pending</option>
                  <option value="failed">Failed</option>
                </select>
              </div>
            </div>

            <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
              <table className="min-w-full divide-y divide-white/10">
                <thead>
                  <tr>
                    <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                      Invoice
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Customer
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Amount
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Status
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                      Date
                    </th>
                    <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-white/10">
                  {currentInvoices.map((invoice) => (
                    <tr key={invoice.id} className="hover:bg-white/5 transition-colors duration-200">
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                        {invoice.id}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        <div className="flex items-center">
                          <UserIcon className="h-5 w-5 text-gray-400 mr-2" />
                          <div>
                            <div className="text-white">{invoice.customer}</div>
                            <div className="text-gray-400">{invoice.email}</div>
                          </div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                        {invoice.amount}
                        <div className="text-xs text-gray-500">{invoice.plan}</div>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        {getStatusBadge(invoice.status)}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                        {invoice.date}
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <Button variant="outline" size="sm">
                          View
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between border-t border-white/10 bg-white/5 px-4 py-3 sm:px-6 mt-4 rounded-xl">
                <div className="flex flex-1 justify-between sm:hidden">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
                <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-400">
                      Showing <span className="font-medium text-white">{indexOfFirstInvoice + 1}</span> to{' '}
                      <span className="font-medium text-white">
                        {Math.min(indexOfLastInvoice, filteredInvoices.length)}
                      </span>{' '}
                      of <span className="font-medium text-white">{filteredInvoices.length}</span> results
                    </p>
                  </div>
                  <div>
                    <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-l-md"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      {Array.from({ length: totalPages }).map((_, index) => (
                        <Button
                          key={index}
                          variant={currentPage === index + 1 ? 'glass' : 'outline'}
                          size="sm"
                          className="px-4"
                          onClick={() => setCurrentPage(index + 1)}
                        >
                          {index + 1}
                        </Button>
                      ))}
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-r-md"
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
