import React, { useEffect, useRef, useState } from 'react';
import { Terminal as XTerm } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { SearchAddon } from '@xterm/addon-search';
import '@xterm/xterm/css/xterm.css';
import {
  CommandLineIcon,
  XMarkIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

interface TerminalSession {
  id: string;
  name: string;
  terminal: XTerm;
  fitAddon: FitAddon;
  isActive: boolean;
}

const Terminal: React.FC = () => {
  const terminalRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const [sessions, setSessions] = useState<TerminalSession[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  const [fontSize, setFontSize] = useState(14);
  const [theme, setTheme] = useState<'dark' | 'light'>('dark');

  // Create initial terminal session
  useEffect(() => {
    createNewSession();
  }, []);

  const createNewSession = () => {
    const sessionId = `terminal-${Date.now()}`;
    const sessionName = `Terminal ${sessions.length + 1}`;

    const terminal = new XTerm({
      fontSize,
      fontFamily: 'Consolas, "Courier New", monospace',
      theme: theme === 'dark' ? {
        background: '#1a1a1a',
        foreground: '#ffffff',
        cursor: '#ffffff',
        selection: '#3e3e3e',
        black: '#000000',
        red: '#ff5555',
        green: '#50fa7b',
        yellow: '#f1fa8c',
        blue: '#bd93f9',
        magenta: '#ff79c6',
        cyan: '#8be9fd',
        white: '#bfbfbf',
        brightBlack: '#4d4d4d',
        brightRed: '#ff6e67',
        brightGreen: '#5af78e',
        brightYellow: '#f4f99d',
        brightBlue: '#caa9fa',
        brightMagenta: '#ff92d0',
        brightCyan: '#9aedfe',
        brightWhite: '#e6e6e6'
      } : {
        background: '#ffffff',
        foreground: '#000000',
        cursor: '#000000',
        selection: '#d4d4d4'
      },
      cursorBlink: true,
      cursorStyle: 'block',
      scrollback: 1000,
      tabStopWidth: 4
    });

    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();
    const searchAddon = new SearchAddon();

    terminal.loadAddon(fitAddon);
    terminal.loadAddon(webLinksAddon);
    terminal.loadAddon(searchAddon);

    // Simulate a shell prompt
    terminal.writeln('Welcome to PoolotHost Terminal');
    terminal.writeln('Type "help" for available commands');
    terminal.write('\r\n$ ');

    // Handle user input
    let currentLine = '';
    terminal.onData((data) => {
      const char = data;
      
      if (char === '\r') {
        // Enter key
        terminal.write('\r\n');
        handleCommand(currentLine.trim(), terminal);
        currentLine = '';
        terminal.write('$ ');
      } else if (char === '\u007f') {
        // Backspace
        if (currentLine.length > 0) {
          currentLine = currentLine.slice(0, -1);
          terminal.write('\b \b');
        }
      } else if (char === '\u0003') {
        // Ctrl+C
        terminal.write('^C\r\n$ ');
        currentLine = '';
      } else if (char.charCodeAt(0) >= 32) {
        // Printable characters
        currentLine += char;
        terminal.write(char);
      }
    });

    const newSession: TerminalSession = {
      id: sessionId,
      name: sessionName,
      terminal,
      fitAddon,
      isActive: true
    };

    setSessions(prev => {
      const updated = prev.map(s => ({ ...s, isActive: false }));
      return [...updated, newSession];
    });
    setActiveSessionId(sessionId);

    // Mount terminal after state update
    setTimeout(() => {
      const element = terminalRefs.current[sessionId];
      if (element) {
        terminal.open(element);
        fitAddon.fit();
      }
    }, 100);
  };

  const handleCommand = (command: string, terminal: XTerm) => {
    const args = command.split(' ');
    const cmd = args[0].toLowerCase();

    switch (cmd) {
      case 'help':
        terminal.writeln('Available commands:');
        terminal.writeln('  help          - Show this help message');
        terminal.writeln('  clear         - Clear the terminal');
        terminal.writeln('  ls            - List files and directories');
        terminal.writeln('  pwd           - Print working directory');
        terminal.writeln('  whoami        - Show current user');
        terminal.writeln('  date          - Show current date and time');
        terminal.writeln('  echo [text]   - Echo text to terminal');
        terminal.writeln('  node [file]   - Run Node.js file');
        terminal.writeln('  npm [command] - Run npm command');
        terminal.writeln('  git [command] - Run git command');
        break;
      
      case 'clear':
        terminal.clear();
        break;
      
      case 'ls':
        terminal.writeln('public/');
        terminal.writeln('src/');
        terminal.writeln('package.json');
        terminal.writeln('README.md');
        terminal.writeln('node_modules/');
        break;
      
      case 'pwd':
        terminal.writeln('/home/<USER>/project');
        break;
      
      case 'whoami':
        terminal.writeln('poolothost-user');
        break;
      
      case 'date':
        terminal.writeln(new Date().toString());
        break;
      
      case 'echo':
        terminal.writeln(args.slice(1).join(' '));
        break;
      
      case 'node':
        if (args[1]) {
          terminal.writeln(`Running: node ${args[1]}`);
          terminal.writeln('Hello World!');
        } else {
          terminal.writeln('Usage: node <filename>');
        }
        break;
      
      case 'npm':
        if (args[1]) {
          terminal.writeln(`Running: npm ${args.slice(1).join(' ')}`);
          terminal.writeln('npm command executed successfully');
        } else {
          terminal.writeln('Usage: npm <command>');
        }
        break;
      
      case 'git':
        if (args[1]) {
          terminal.writeln(`Running: git ${args.slice(1).join(' ')}`);
          terminal.writeln('Git command executed');
        } else {
          terminal.writeln('Usage: git <command>');
        }
        break;
      
      case '':
        // Empty command, just show prompt
        break;
      
      default:
        terminal.writeln(`Command not found: ${cmd}`);
        terminal.writeln('Type "help" for available commands');
        break;
    }
  };

  const closeSession = (sessionId: string) => {
    setSessions(prev => {
      const updated = prev.filter(s => s.id !== sessionId);
      if (updated.length > 0 && activeSessionId === sessionId) {
        setActiveSessionId(updated[updated.length - 1].id);
      } else if (updated.length === 0) {
        setActiveSessionId(null);
      }
      return updated;
    });
  };

  const switchSession = (sessionId: string) => {
    setSessions(prev => prev.map(s => ({ ...s, isActive: s.id === sessionId })));
    setActiveSessionId(sessionId);
    
    // Fit terminal when switching
    setTimeout(() => {
      const session = sessions.find(s => s.id === sessionId);
      if (session) {
        session.fitAddon.fit();
      }
    }, 100);
  };

  const clearActiveTerminal = () => {
    const activeSession = sessions.find(s => s.id === activeSessionId);
    if (activeSession) {
      activeSession.terminal.clear();
      activeSession.terminal.write('$ ');
    }
  };

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      sessions.forEach(session => {
        session.fitAddon.fit();
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [sessions]);

  return (
    <div className="h-full flex flex-col bg-black">
      {/* Terminal Header */}
      <div className="flex items-center justify-between p-2 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center space-x-2">
          <CommandLineIcon className="h-5 w-5 text-green-400" />
          <span className="text-sm font-medium text-white">Terminal</span>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Font Size Controls */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setFontSize(Math.max(10, fontSize - 1))}
              className="p-1 text-gray-400 hover:text-white text-xs"
            >
              A-
            </button>
            <span className="text-xs text-gray-400 w-8 text-center">{fontSize}</span>
            <button
              onClick={() => setFontSize(Math.min(24, fontSize + 1))}
              className="p-1 text-gray-400 hover:text-white text-xs"
            >
              A+
            </button>
          </div>

          <button
            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            className="p-1 text-gray-400 hover:text-white"
            title="Toggle Theme"
          >
            <Cog6ToothIcon className="h-4 w-4" />
          </button>

          <button
            onClick={clearActiveTerminal}
            className="p-1 text-gray-400 hover:text-white"
            title="Clear Terminal"
          >
            <ArrowPathIcon className="h-4 w-4" />
          </button>

          <button
            onClick={createNewSession}
            className="p-1 text-gray-400 hover:text-white"
            title="New Terminal"
          >
            <PlusIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Terminal Tabs */}
      {sessions.length > 0 && (
        <div className="flex bg-gray-900 border-b border-gray-700">
          {sessions.map(session => (
            <div
              key={session.id}
              className={`flex items-center px-3 py-2 cursor-pointer border-r border-gray-700 ${
                session.id === activeSessionId
                  ? 'bg-black text-white'
                  : 'bg-gray-800 text-gray-400 hover:text-white'
              }`}
              onClick={() => switchSession(session.id)}
            >
              <span className="text-sm mr-2">{session.name}</span>
              {sessions.length > 1 && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    closeSession(session.id);
                  }}
                  className="text-gray-500 hover:text-white"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Terminal Content */}
      <div className="flex-1 relative">
        {sessions.map(session => (
          <div
            key={session.id}
            ref={(el) => terminalRefs.current[session.id] = el}
            className={`absolute inset-0 ${
              session.id === activeSessionId ? 'block' : 'hidden'
            }`}
          />
        ))}
        
        {sessions.length === 0 && (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <CommandLineIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg mb-2">No Terminal Sessions</p>
              <button
                onClick={createNewSession}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Create New Terminal
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Terminal Guide */}
      <div className="p-2 bg-gray-900 border-t border-gray-700">
        <p className="text-xs text-gray-500">
          💡 <strong>Quick Guide:</strong> Type "help" for commands, Ctrl+C to interrupt, multiple tabs supported
        </p>
      </div>
    </div>
  );
};

export default Terminal;
