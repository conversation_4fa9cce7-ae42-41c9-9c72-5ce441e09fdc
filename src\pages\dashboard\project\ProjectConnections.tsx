import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { 
  ClipboardDocumentIcon, 
  ServerIcon, 
  KeyIcon, 
  UserIcon, 
  GlobeAltIcon,
  LockClosedIcon,
  DocumentDuplicateIcon,
  ArrowPathIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

export default function ProjectConnections() {
  const project = useOutletContext<any>();
  const [showFtpPassword, setShowFtpPassword] = useState(false);
  const [showSshPassword, setShowSshPassword] = useState(false);
  const [copiedField, setCopiedField] = useState('');
  
  // Function to copy text to clipboard
  const copyToClipboard = (text: string, field: string) => {
    navigator.clipboard.writeText(text);
    setCopiedField(field);
    setTimeout(() => setCopiedField(''), 2000);
  };

  // Function to toggle password visibility
  const togglePasswordVisibility = (type: 'ftp' | 'ssh') => {
    if (type === 'ftp') {
      setShowFtpPassword(!showFtpPassword);
    } else {
      setShowSshPassword(!showSshPassword);
    }
  };

  // Function to generate a new password
  const regeneratePassword = (type: 'ftp' | 'ssh') => {
    // In a real app, this would call an API to generate a new password
    console.log(`Regenerating ${type} password`);
    // For demo purposes, we'll just toggle the visibility off
    if (type === 'ftp') {
      setShowFtpPassword(false);
    } else {
      setShowSshPassword(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-white">FTP Connection Details</h2>
            <p className="text-sm text-gray-400">Use these credentials to connect via FTP and upload files directly</p>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            icon={<ArrowPathIcon className="h-4 w-4" />}
            onClick={() => regeneratePassword('ftp')}
          >
            Reset Password
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Hostname / IP Address
                </label>
                <div className="flex">
                  <div className="relative flex-grow">
                    <div className="flex items-center">
                      <ServerIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                      <input
                        type="text"
                        className="block w-full rounded-l-lg border-0 py-2 pl-10 pr-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        value={project.connections?.ftp?.host || 'ftp.poolothost.com'}
                        readOnly
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-white/10 rounded-r-lg bg-white/5 text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                    onClick={() => copyToClipboard(project.connections?.ftp?.host || 'ftp.poolothost.com', 'ftp-host')}
                  >
                    {copiedField === 'ftp-host' ? (
                      <ClipboardDocumentIcon className="h-5 w-5 text-green-400" />
                    ) : (
                      <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Port
                </label>
                <div className="flex">
                  <div className="relative flex-grow">
                    <div className="flex items-center">
                      <GlobeAltIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                      <input
                        type="text"
                        className="block w-full rounded-l-lg border-0 py-2 pl-10 pr-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        value={project.connections?.ftp?.port || '21'}
                        readOnly
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-white/10 rounded-r-lg bg-white/5 text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                    onClick={() => copyToClipboard(project.connections?.ftp?.port || '21', 'ftp-port')}
                  >
                    {copiedField === 'ftp-port' ? (
                      <ClipboardDocumentIcon className="h-5 w-5 text-green-400" />
                    ) : (
                      <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Username
                </label>
                <div className="flex">
                  <div className="relative flex-grow">
                    <div className="flex items-center">
                      <UserIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                      <input
                        type="text"
                        className="block w-full rounded-l-lg border-0 py-2 pl-10 pr-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        value={project.connections?.ftp?.username || `${project.name.toLowerCase().replace(/\s+/g, '-')}`}
                        readOnly
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-white/10 rounded-r-lg bg-white/5 text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                    onClick={() => copyToClipboard(project.connections?.ftp?.username || `${project.name.toLowerCase().replace(/\s+/g, '-')}`, 'ftp-username')}
                  >
                    {copiedField === 'ftp-username' ? (
                      <ClipboardDocumentIcon className="h-5 w-5 text-green-400" />
                    ) : (
                      <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Password
                </label>
                <div className="flex">
                  <div className="relative flex-grow">
                    <div className="flex items-center">
                      <LockClosedIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                      <input
                        type={showFtpPassword ? "text" : "password"}
                        className="block w-full rounded-l-lg border-0 py-2 pl-10 pr-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        value={project.connections?.ftp?.password || "securePassword123"}
                        readOnly
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-white/10 bg-white/5 text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                    onClick={() => togglePasswordVisibility('ftp')}
                  >
                    <KeyIcon className="h-5 w-5 text-gray-400" />
                  </button>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-white/10 rounded-r-lg bg-white/5 text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                    onClick={() => copyToClipboard(project.connections?.ftp?.password || "securePassword123", 'ftp-password')}
                  >
                    {copiedField === 'ftp-password' ? (
                      <ClipboardDocumentIcon className="h-5 w-5 text-green-400" />
                    ) : (
                      <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 p-4 rounded-lg bg-secondary-900/50 border border-secondary-500/30">
          <div className="flex items-start">
            <InformationCircleIcon className="h-5 w-5 text-secondary-400 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <p className="text-sm text-gray-300">
                Use these credentials with any standard FTP client like FileZilla, WinSCP, or Cyberduck to upload files directly to your hosting environment.
              </p>
            </div>
          </div>
        </div>
      </Card>
      
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-white">SSH Connection Details (Putty/WinSCP)</h2>
            <p className="text-sm text-gray-400">Use these credentials to connect via SSH for terminal access</p>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            icon={<ArrowPathIcon className="h-4 w-4" />}
            onClick={() => regeneratePassword('ssh')}
          >
            Reset Password
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Hostname / IP Address
                </label>
                <div className="flex">
                  <div className="relative flex-grow">
                    <div className="flex items-center">
                      <ServerIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                      <input
                        type="text"
                        className="block w-full rounded-l-lg border-0 py-2 pl-10 pr-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        value={project.connections?.ssh?.host || 'ssh.poolothost.com'}
                        readOnly
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-white/10 rounded-r-lg bg-white/5 text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                    onClick={() => copyToClipboard(project.connections?.ssh?.host || 'ssh.poolothost.com', 'ssh-host')}
                  >
                    {copiedField === 'ssh-host' ? (
                      <ClipboardDocumentIcon className="h-5 w-5 text-green-400" />
                    ) : (
                      <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Port
                </label>
                <div className="flex">
                  <div className="relative flex-grow">
                    <div className="flex items-center">
                      <GlobeAltIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                      <input
                        type="text"
                        className="block w-full rounded-l-lg border-0 py-2 pl-10 pr-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        value={project.connections?.ssh?.port || '22'}
                        readOnly
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-white/10 rounded-r-lg bg-white/5 text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                    onClick={() => copyToClipboard(project.connections?.ssh?.port || '22', 'ssh-port')}
                  >
                    {copiedField === 'ssh-port' ? (
                      <ClipboardDocumentIcon className="h-5 w-5 text-green-400" />
                    ) : (
                      <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Username
                </label>
                <div className="flex">
                  <div className="relative flex-grow">
                    <div className="flex items-center">
                      <UserIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                      <input
                        type="text"
                        className="block w-full rounded-l-lg border-0 py-2 pl-10 pr-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        value={project.connections?.ssh?.username || `${project.name.toLowerCase().replace(/\s+/g, '-')}`}
                        readOnly
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-white/10 rounded-r-lg bg-white/5 text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                    onClick={() => copyToClipboard(project.connections?.ssh?.username || `${project.name.toLowerCase().replace(/\s+/g, '-')}`, 'ssh-username')}
                  >
                    {copiedField === 'ssh-username' ? (
                      <ClipboardDocumentIcon className="h-5 w-5 text-green-400" />
                    ) : (
                      <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Password
                </label>
                <div className="flex">
                  <div className="relative flex-grow">
                    <div className="flex items-center">
                      <LockClosedIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                      <input
                        type={showSshPassword ? "text" : "password"}
                        className="block w-full rounded-l-lg border-0 py-2 pl-10 pr-3 text-white ring-1 ring-inset ring-white/10 
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        value={project.connections?.ssh?.password || "securePassword123"}
                        readOnly
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-white/10 bg-white/5 text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                    onClick={() => togglePasswordVisibility('ssh')}
                  >
                    <KeyIcon className="h-5 w-5 text-gray-400" />
                  </button>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-l-0 border-white/10 rounded-r-lg bg-white/5 text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                    onClick={() => copyToClipboard(project.connections?.ssh?.password || "securePassword123", 'ssh-password')}
                  >
                    {copiedField === 'ssh-password' ? (
                      <ClipboardDocumentIcon className="h-5 w-5 text-green-400" />
                    ) : (
                      <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 p-4 rounded-lg bg-secondary-900/50 border border-secondary-500/30">
          <div className="flex items-start">
            <InformationCircleIcon className="h-5 w-5 text-secondary-400 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <p className="text-sm text-gray-300">
                Use these credentials with SSH clients like Putty or WinSCP to access your server's terminal or transfer files securely.
              </p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
