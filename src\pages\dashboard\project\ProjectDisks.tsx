import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { 
  CircleStackIcon,
  PlusIcon,
  TrashIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

export default function ProjectDisks() {
  const project = useOutletContext<any>();
  const [isAddingDisk, setIsAddingDisk] = useState(false);
  const [diskName, setDiskName] = useState('');
  const [diskSize, setDiskSize] = useState(10);
  const [mountPath, setMountPath] = useState('/data');
  const [isConfirmingDelete, setIsConfirmingDelete] = useState<string | null>(null);
  
  // Sample disks data
  const disks = [
    {
      id: 'disk-1',
      name: 'main-storage',
      size: 10,
      used: 3.2,
      mountPath: '/data',
      status: 'attached',
      created: '30 days ago',
    },
    {
      id: 'disk-2',
      name: 'media-storage',
      size: 20,
      used: 15.7,
      mountPath: '/media',
      status: 'attached',
      created: '25 days ago',
    },
    {
      id: 'disk-3',
      name: 'backup-storage',
      size: 50,
      used: 22.3,
      mountPath: '/backup',
      status: 'detached',
      created: '15 days ago',
    },
  ];
  
  // Function to add a new disk
  const handleAddDisk = () => {
    // In a real app, this would call an API to create a new disk
    console.log('Adding disk...');
    setIsAddingDisk(false);
    setDiskName('');
    setDiskSize(10);
    setMountPath('/data');
    // For demo purposes, we'll just show a success message
    alert('Disk added successfully!');
  };
  
  // Function to delete a disk
  const handleDeleteDisk = (diskId: string) => {
    // In a real app, this would call an API to delete the disk
    console.log(`Deleting disk ${diskId}...`);
    setIsConfirmingDelete(null);
    // For demo purposes, we'll just show a success message
    alert('Disk deleted successfully!');
  };
  
  // Function to get status badge
  const getStatusBadge = (status: string) => {
    if (status === 'attached') {
      return (
        <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
          <CheckCircleIcon className="h-3 w-3 mr-1 text-green-400" />
          Attached
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
          <XCircleIcon className="h-3 w-3 mr-1 text-gray-400" />
          Detached
        </span>
      );
    }
  };
  
  // Function to calculate usage percentage
  const getUsagePercentage = (used: number, total: number) => {
    return (used / total) * 100;
  };
  
  // Function to get usage color
  const getUsageColor = (percentage: number) => {
    if (percentage < 50) return 'bg-green-500';
    if (percentage < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-white">Persistent Disks</h2>
          <p className="text-sm text-gray-400">Manage storage volumes for your application</p>
        </div>
        <Button 
          variant="glass" 
          size="sm"
          icon={<PlusIcon className="h-4 w-4" />}
          onClick={() => setIsAddingDisk(true)}
        >
          Add Disk
        </Button>
      </div>
      
      {isAddingDisk && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Add New Disk</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Disk Name
              </label>
              <input
                type="text"
                className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                  placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                placeholder="e.g., data-storage"
                value={diskName}
                onChange={(e) => setDiskName(e.target.value)}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Disk Size (GB)
              </label>
              <input
                type="number"
                min="1"
                max="500"
                className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                  placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                value={diskSize}
                onChange={(e) => setDiskSize(parseInt(e.target.value))}
              />
              <p className="mt-1 text-xs text-gray-500">Estimated cost: ${(diskSize * 0.10).toFixed(2)}/month</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Mount Path
              </label>
              <input
                type="text"
                className="block w-full rounded-lg border-0 py-2 px-3 text-white ring-1 ring-inset ring-white/10 
                  placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                  sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                placeholder="/data"
                value={mountPath}
                onChange={(e) => setMountPath(e.target.value)}
              />
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setIsAddingDisk(false)}
              >
                Cancel
              </Button>
              <Button 
                variant="glass" 
                size="sm"
                icon={<PlusIcon className="h-4 w-4" />}
                onClick={handleAddDisk}
                disabled={!diskName || diskSize <= 0 || !mountPath}
              >
                Add Disk
              </Button>
            </div>
          </div>
        </Card>
      )}
      
      <div className="space-y-4">
        {disks.map((disk) => (
          <Card key={disk.id} className="p-6">
            <div className="flex flex-col md:flex-row justify-between gap-4">
              <div className="flex-grow">
                <div className="flex items-center mb-2">
                  {getStatusBadge(disk.status)}
                  <h3 className="text-lg font-semibold text-white ml-2">{disk.name}</h3>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-300">
                    <CircleStackIcon className="h-4 w-4 mr-2 text-gray-400" />
                    Size: {disk.size} GB
                    <span className="mx-2 text-gray-500">•</span>
                    Mount Path: {disk.mountPath}
                    <span className="mx-2 text-gray-500">•</span>
                    Created: {disk.created}
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-400">Usage: {disk.used} GB of {disk.size} GB ({Math.round(getUsagePercentage(disk.used, disk.size))}%)</span>
                      <span className={`${getUsagePercentage(disk.used, disk.size) > 80 ? 'text-red-400' : 'text-gray-400'}`}>
                        {disk.size - disk.used} GB free
                      </span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2.5">
                      <div 
                        className={`${getUsageColor(getUsagePercentage(disk.used, disk.size))} h-2.5 rounded-full`} 
                        style={{ width: `${getUsagePercentage(disk.used, disk.size)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap gap-2 items-start">
                {disk.status === 'attached' ? (
                  <Button 
                    variant="outline" 
                    size="sm"
                    icon={<XCircleIcon className="h-4 w-4" />}
                  >
                    Detach
                  </Button>
                ) : (
                  <Button 
                    variant="outline" 
                    size="sm"
                    icon={<CheckCircleIcon className="h-4 w-4" />}
                  >
                    Attach
                  </Button>
                )}
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={<ArrowPathIcon className="h-4 w-4" />}
                >
                  Resize
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  icon={<TrashIcon className="h-4 w-4" />}
                  onClick={() => setIsConfirmingDelete(disk.id)}
                >
                  Delete
                </Button>
              </div>
            </div>
            
            {isConfirmingDelete === disk.id && (
              <div className="mt-4 p-3 rounded-lg bg-red-900/30 border border-red-500/30">
                <div className="flex items-start">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-white mb-2">Are you sure you want to delete this disk?</p>
                    <p className="text-xs text-gray-300 mb-3">This action cannot be undone. All data stored on this disk will be permanently deleted.</p>
                    <div className="flex space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsConfirmingDelete(null)}
                      >
                        Cancel
                      </Button>
                      <Button 
                        variant="glass" 
                        size="sm"
                        icon={<TrashIcon className="h-4 w-4" />}
                        onClick={() => handleDeleteDisk(disk.id)}
                      >
                        Confirm Delete
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>
      
      {disks.length === 0 && (
        <Card className="p-8 text-center">
          <CircleStackIcon className="h-12 w-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">No Persistent Disks</h3>
          <p className="text-gray-400 mb-6">Add persistent disks to store data that needs to survive container restarts.</p>
          <Button 
            variant="glass" 
            icon={<PlusIcon className="h-4 w-4" />}
            onClick={() => setIsAddingDisk(true)}
          >
            Add First Disk
          </Button>
        </Card>
      )}
      
      <Card className="p-6">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-secondary-400 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h3 className="text-base font-semibold text-white mb-2">About Persistent Disks</h3>
            <p className="text-sm text-gray-300 mb-2">
              Persistent disks provide durable storage for your applications. Data stored on these disks persists even when your application restarts or redeploys.
            </p>
            <ul className="list-disc list-inside text-sm text-gray-300 space-y-1">
              <li>Disks can be attached to one application at a time</li>
              <li>Pricing is $0.10 per GB per month</li>
              <li>Disks can be resized but cannot be shrunk</li>
              <li>Backups are performed daily and retained for 7 days</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
}
