import { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import Card from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import {
  ArrowPathIcon,
  TrashIcon,
  PauseIcon,
  PlayIcon,
  ArrowTopRightOnSquareIcon,
  CodeBracketIcon,
  CommandLineIcon,
  CogIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

export default function ProjectManage() {
  const project = useOutletContext<any>();
  const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
  const [isConfirmingRestart, setIsConfirmingRestart] = useState(false);

  // Function to handle project restart
  const handleRestart = () => {
    // In a real app, this would call an API to restart the project
    console.log('Restarting project...');
    setIsConfirmingRestart(false);
    // For demo purposes, we'll just show a success message
    alert('Project restarted successfully!');
  };

  // Function to handle project deletion
  const handleDelete = () => {
    // In a real app, this would call an API to delete the project
    console.log('Deleting project...');
    setIsConfirmingDelete(false);
    // For demo purposes, we'll just show a success message
    alert('Project deleted successfully!');
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-white">Project Management</h2>
          <p className="text-sm text-gray-400">Manage your project settings and operations</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            as="a"
            href={project?.url}
            target="_blank"
            rel="noopener noreferrer"
            icon={<ArrowTopRightOnSquareIcon className="h-4 w-4" />}
          >
            Open Site
          </Button>
          <Button
            variant="glass"
            size="sm"
            icon={project?.status === 'running' ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
          >
            {project?.status === 'running' ? 'Stop' : 'Start'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Project Status</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 rounded-lg bg-white/5 border border-white/10">
              <div className="flex items-center">
                <div className="mr-3">
                  {project?.status === 'running' ? (
                    <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
                      <span className="h-1.5 w-1.5 rounded-full bg-green-400 mr-1.5 animate-pulse"></span>
                      Running
                    </span>
                  ) : (
                    <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
                      <PauseIcon className="h-3 w-3 mr-1 text-gray-400" />
                      Stopped
                    </span>
                  )}
                </div>
                <div>
                  <p className="text-sm text-white">Current Status</p>
                  <p className="text-xs text-gray-400">Last changed: 2 hours ago</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                icon={project?.status === 'running' ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
              >
                {project?.status === 'running' ? 'Stop' : 'Start'}
              </Button>
            </div>

            <div className="flex justify-between items-center p-3 rounded-lg bg-white/5 border border-white/10">
              <div className="flex items-center">
                <div className="mr-3">
                  <span className="inline-flex items-center rounded-full bg-blue-900/30 border border-blue-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-blue-300">
                    <CodeBracketIcon className="h-3 w-3 mr-1 text-blue-400" />
                    {project?.framework}
                  </span>
                </div>
                <div>
                  <p className="text-sm text-white">Framework</p>
                  <p className="text-xs text-gray-400">Runtime: Node.js 18.x</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                icon={<CogIcon className="h-4 w-4" />}
              >
                Configure
              </Button>
            </div>

            <div className="flex justify-between items-center p-3 rounded-lg bg-white/5 border border-white/10">
              <div className="flex items-center">
                <div className="mr-3">
                  <span className="inline-flex items-center rounded-full bg-purple-900/30 border border-purple-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-purple-300">
                    <ShieldCheckIcon className="h-3 w-3 mr-1 text-purple-400" />
                    Secure
                  </span>
                </div>
                <div>
                  <p className="text-sm text-white">HTTPS Enabled</p>
                  <p className="text-xs text-gray-400">SSL Certificate: Auto-renewed</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                icon={<CogIcon className="h-4 w-4" />}
              >
                Manage SSL
              </Button>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
          <div className="space-y-4">
            <div className="p-3 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200">
              <Button
                variant="outline"
                fullWidth
                className="justify-start"
                icon={<ArrowPathIcon className="h-5 w-5" />}
                onClick={() => setIsConfirmingRestart(true)}
              >
                Restart Application
              </Button>
              {isConfirmingRestart && (
                <div className="mt-3 p-3 rounded-lg bg-yellow-900/30 border border-yellow-500/30">
                  <div className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5 mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-sm text-white mb-2">Are you sure you want to restart this application?</p>
                      <p className="text-xs text-gray-300 mb-3">This will cause a brief downtime while the application restarts.</p>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsConfirmingRestart(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="glass"
                          size="sm"
                          icon={<ArrowPathIcon className="h-4 w-4" />}
                          onClick={handleRestart}
                        >
                          Confirm Restart
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="p-3 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200">
              <Button
                variant="outline"
                fullWidth
                className="justify-start"
                icon={<CommandLineIcon className="h-5 w-5" />}
              >
                Open Shell
              </Button>
            </div>

            <div className="p-3 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200">
              <Button
                variant="outline"
                fullWidth
                className="justify-start text-red-400 hover:text-red-300"
                icon={<TrashIcon className="h-5 w-5" />}
                onClick={() => setIsConfirmingDelete(true)}
              >
                Delete Project
              </Button>
              {isConfirmingDelete && (
                <div className="mt-3 p-3 rounded-lg bg-red-900/30 border border-red-500/30">
                  <div className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5 mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-sm text-white mb-2">Are you sure you want to delete this project?</p>
                      <p className="text-xs text-gray-300 mb-3">This action cannot be undone. All data will be permanently deleted.</p>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsConfirmingDelete(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="glass"
                          size="sm"
                          icon={<TrashIcon className="h-4 w-4" />}
                          onClick={handleDelete}
                        >
                          Confirm Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {project?.events?.slice(0, 5).map((event: any, index: number) => (
            <div
              key={index}
              className="p-3 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors duration-200"
            >
              <div className="flex items-center">
                <div className="mr-3">
                  {event.status === 'success' ? (
                    <CheckCircleIcon className="h-5 w-5 text-green-400" />
                  ) : event.status === 'failed' ? (
                    <XCircleIcon className="h-5 w-5 text-red-400" />
                  ) : (
                    <ArrowPathIcon className="h-5 w-5 text-blue-400" />
                  )}
                </div>
                <div>
                  <p className="text-sm text-white">{event.message}</p>
                  <p className="text-xs text-gray-400">{event.timestamp}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
