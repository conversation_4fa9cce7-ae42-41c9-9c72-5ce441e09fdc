import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/20/solid';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import Card from '../ui/Card';
import { useEffect, useState } from 'react';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler);

interface Stat {
  name: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
}

interface HostingStatsProps {
  stats: Stat[];
}

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function HostingStats({ stats }: HostingStatsProps) {
  const [animatedData, setAnimatedData] = useState<number[][]>([
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0]
  ]);

  // Real data for the chart
  const finalData = [
    [65, 59, 80, 81, 56, 55, 40],
    [28, 48, 40, 19, 86, 27, 90]
  ];

  // Animate the chart data on component mount
  useEffect(() => {
    const animationDuration = 1500; // ms
    const steps = 30;
    const stepDuration = animationDuration / steps;

    let currentStep = 0;

    const interval = setInterval(() => {
      if (currentStep >= steps) {
        clearInterval(interval);
        setAnimatedData(finalData);
        return;
      }

      const progress = (currentStep + 1) / steps;
      const newData = finalData.map((dataset) =>
        dataset.map((value) => Math.round(value * progress))
      );

      setAnimatedData(newData);
      currentStep++;
    }, stepDuration);

    return () => clearInterval(interval);
  }, []);

  // Chart data with animated values
  const chartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
    datasets: [
      {
        label: 'CPU Usage',
        data: animatedData[0],
        fill: true,
        backgroundColor: 'rgba(139, 92, 246, 0.3)',
        borderColor: 'rgba(139, 92, 246, 1)',
        borderWidth: 2,
        tension: 0.4,
        pointBackgroundColor: 'rgba(139, 92, 246, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(139, 92, 246, 1)',
        pointRadius: 4,
      },
      {
        label: 'Memory Usage',
        data: animatedData[1],
        fill: true,
        backgroundColor: 'rgba(14, 165, 233, 0.3)',
        borderColor: 'rgba(14, 165, 233, 1)',
        borderWidth: 2,
        tension: 0.4,
        pointBackgroundColor: 'rgba(14, 165, 233, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(14, 165, 233, 1)',
        pointRadius: 4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Poppins',
            size: 12,
          },
          boxWidth: 15,
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        titleFont: {
          family: 'Poppins',
          size: 13,
        },
        bodyFont: {
          family: 'Poppins',
          size: 12,
        },
        padding: 12,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
    },
    animation: {
      duration: 1000,
      easing: 'easeOutQuart',
    },
  };

  return (
    <div>
      <h3 className="text-base font-semibold leading-6 text-white mb-2">Last 30 days</h3>
      <dl className="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((item, index) => (
          <Card
            key={item.name}
            className="px-4 py-5 sm:p-6 overflow-hidden relative"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            {/* Decorative gradient */}
            <div className="absolute -z-10 inset-0 opacity-30">
              <div className={`absolute ${index % 2 === 0 ? '-top-10 -right-10' : '-bottom-10 -left-10'} w-24 h-24 rounded-full blur-xl ${item.changeType === 'increase' ? 'bg-green-500/30' : 'bg-red-500/30'}`}></div>
            </div>

            <dt className="text-sm font-medium text-gray-300 truncate">{item.name}</dt>
            <dd className="mt-1 flex items-baseline justify-between md:block lg:flex">
              <div className="flex items-baseline text-2xl font-semibold text-white">
                {item.value}
              </div>

              <div
                className={classNames(
                  item.changeType === 'increase' ? 'bg-green-900/50 text-green-300 border border-green-500/30' : 'bg-red-900/50 text-red-300 border border-red-500/30',
                  'inline-flex items-baseline rounded-full px-2.5 py-0.5 text-sm font-medium md:mt-2 lg:mt-0 backdrop-blur-sm'
                )}
              >
                {item.changeType === 'increase' ? (
                  <ArrowUpIcon
                    className="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-green-400"
                    aria-hidden="true"
                  />
                ) : (
                  <ArrowDownIcon
                    className="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-red-400"
                    aria-hidden="true"
                  />
                )}
                <span className="sr-only">{item.changeType === 'increase' ? 'Increased' : 'Decreased'} by</span>
                {item.change}
              </div>
            </dd>
          </Card>
        ))}
      </dl>

      <div className="mt-8">
        <Card
          className="p-6 relative overflow-hidden"
          hover3D={false}
        >
          {/* Decorative elements */}
          <div className="absolute -z-10 -top-20 -right-20 w-64 h-64 bg-secondary-600/10 rounded-full blur-3xl"></div>
          <div className="absolute -z-10 -bottom-20 -left-20 w-64 h-64 bg-primary-600/10 rounded-full blur-3xl"></div>

          <h3 className="text-base font-semibold leading-6 text-white mb-6">Resource Usage</h3>
          <div className="h-80 relative">
            <Line data={chartData} options={chartOptions} />
          </div>
        </Card>
      </div>
    </div>
  );
}
